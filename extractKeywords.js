#!/usr/bin/env node

const fs = require('fs');
const yaml = require('js-yaml');

/**
 * 从 figma.yml 文件中提取所有关键词名称
 */
function extractAllKeywordNames(figmaFilePath = './figma.yml') {
    try {
        // 检查文件是否存在
        if (!fs.existsSync(figmaFilePath)) {
            throw new Error(`文件不存在: ${figmaFilePath}`);
        }

        // 读取并解析 YAML 文件
        console.log(`正在读取文件: ${figmaFilePath}`);
        const fileContent = fs.readFileSync(figmaFilePath, 'utf8');
        const figmaData = yaml.load(fileContent);
        
        const keywordNames = new Set(); // 使用 Set 去重

        // 1. 从组件中提取名称
        console.log('正在提取组件名称...');
        const components = figmaData.metadata?.components || {};
        Object.values(components).forEach(component => {
            if (component.name) {
                keywordNames.add(component.name);
            }
        });
        console.log(`从组件中提取到 ${Object.keys(components).length} 个条目`);

        // 2. 从组件集中提取名称
        console.log('正在提取组件集名称...');
        const componentSets = figmaData.metadata?.componentSets || {};
        Object.values(componentSets).forEach(componentSet => {
            if (componentSet.name) {
                keywordNames.add(componentSet.name);
            }
        });
        console.log(`从组件集中提取到 ${Object.keys(componentSets).length} 个条目`);

        // 3. 从节点中提取名称
        console.log('正在提取节点名称...');
        let nodeCount = 0;
        function extractFromNodes(nodes) {
            if (!Array.isArray(nodes)) return;
            
            nodes.forEach(node => {
                if (node.name) {
                    keywordNames.add(node.name);
                    nodeCount++;
                }
                
                if (node.children) {
                    extractFromNodes(node.children);
                }
            });
        }

        if (figmaData.nodes) {
            extractFromNodes(figmaData.nodes);
        }
        console.log(`从节点中提取到 ${nodeCount} 个条目`);

        // 转换为数组并排序
        const result = Array.from(keywordNames).sort();
        console.log(`去重后总计: ${result.length} 个唯一关键词\n`);
        
        return result;

    } catch (error) {
        console.error('提取关键词时发生错误:', error.message);
        return [];
    }
}

/**
 * 主函数
 */
function main() {
    console.log('=== Figma 关键词提取器 ===\n');
    
    // 提取关键词
    const keywords = extractAllKeywordNames();
    
    if (keywords.length === 0) {
        console.log('未找到任何关键词或提取失败');
        process.exit(1);
    }

    // 输出结果
    console.log(`=== 找到 ${keywords.length} 个关键词 ===\n`);
    
    keywords.forEach((keyword, index) => {
        console.log(`${(index + 1).toString().padStart(3, ' ')}. ${keyword}`);
    });

    console.log(`\n=== 总计: ${keywords.length} 个关键词 ===`);

    // 将结果保存到文件
    try {
        const outputFile = 'keywords.json';
        const outputData = {
            extractedAt: new Date().toISOString(),
            totalCount: keywords.length,
            keywords: keywords
        };
        
        fs.writeFileSync(outputFile, JSON.stringify(outputData, null, 2), 'utf8');
        console.log(`\n✅ 关键词已保存到: ${outputFile}`);
        
        // 同时保存为简单的文本文件
        const txtFile = 'keywords.txt';
        fs.writeFileSync(txtFile, keywords.join('\n'), 'utf8');
        console.log(`✅ 关键词列表已保存到: ${txtFile}`);
        
    } catch (error) {
        console.error('保存文件时出错:', error.message);
    }
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
    main();
}

// 导出函数供其他模块使用
module.exports = { extractAllKeywordNames };
