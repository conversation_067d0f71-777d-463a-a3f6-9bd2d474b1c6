#!/usr/bin/env node

const fs = require('fs');
const yaml = require('js-yaml');

/**
 * 加载 Figma 数据
 */
function loadFigmaData(filePath = './figma.yml') {
    try {
        if (!fs.existsSync(filePath)) {
            throw new Error(`文件不存在: ${filePath}`);
        }
        const fileContent = fs.readFileSync(filePath, 'utf8');
        return yaml.load(fileContent);
    } catch (error) {
        throw new Error(`加载 Figma 数据失败: ${error.message}`);
    }
}

/**
 * 根据关键词过滤数据
 */
function filterByKeywords(figmaData, keywords) {
    console.log(`开始过滤，关键词: ${keywords.join(', ')}`);

    // 初始化过滤结果
    const filteredData = {
        metadata: { components: {}, componentSets: {} },
        nodes: [],
        layouts: {},
        styles: {},
        fills: {},
        strokes: {}
    };

    // 1. 过滤组件和组件集
    filterComponents(figmaData, filteredData, keywords);

    // 2. 过滤节点
    filterNodes(figmaData, filteredData, keywords);

    // 3. 收集关联的布局数据
    collectRelatedLayouts(figmaData, filteredData);

    // 4. 收集关联的样式数据
    collectRelatedStyles(figmaData, filteredData);

    // 5. 统计过滤结果
    const stats = getFilterStats(filteredData);
    console.log('过滤完成:', stats);

    return filteredData;
}

/**
 * 过滤组件和组件集
 */
function filterComponents(figmaData, filteredData, keywords) {
    // 过滤组件
    const components = figmaData.metadata?.components || {};
    Object.entries(components).forEach(([id, component]) => {
        if (matchesKeywords(component.name, keywords)) {
            filteredData.metadata.components[id] = component;
        }
    });

    // 过滤组件集
    const componentSets = figmaData.metadata?.componentSets || {};
    Object.entries(componentSets).forEach(([id, componentSet]) => {
        if (matchesKeywords(componentSet.name, keywords)) {
            filteredData.metadata.componentSets[id] = componentSet;
        }
    });

    // 如果找到了组件集，也要包含其下的所有组件
    Object.keys(filteredData.metadata.componentSets).forEach(setId => {
        Object.entries(components).forEach(([compId, comp]) => {
            if (comp.componentSetId === setId) {
                filteredData.metadata.components[compId] = comp;
            }
        });
    });
}

/**
 * 过滤节点
 */
function filterNodes(figmaData, filteredData, keywords) {
    const filteredNodeIds = new Set();

    const filterNodesRecursive = (nodes) => {
        if (!Array.isArray(nodes)) return [];

        const filtered = [];
        nodes.forEach(node => {
            let shouldInclude = false;

            // 检查节点名称是否匹配
            if (node.name && matchesKeywords(node.name, keywords)) {
                shouldInclude = true;
            }

            // 检查是否使用了匹配的组件
            if (node.componentId && filteredData.metadata.components[node.componentId]) {
                shouldInclude = true;
            }

            // 递归处理子节点
            let filteredChildren = [];
            if (node.children) {
                filteredChildren = filterNodesRecursive(node.children);
                if (filteredChildren.length > 0) {
                    shouldInclude = true;
                }
            }

            if (shouldInclude) {
                const filteredNode = { ...node };
                if (filteredChildren.length > 0) {
                    filteredNode.children = filteredChildren;
                }
                filtered.push(filteredNode);
                filteredNodeIds.add(node.id);
            }
        });

        return filtered;
    };

    if (figmaData.nodes) {
        filteredData.nodes = filterNodesRecursive(figmaData.nodes);
    }

    return filteredNodeIds;
}

/**
 * 收集关联的布局数据
 */
function collectRelatedLayouts(figmaData, filteredData) {
    const layoutIds = new Set();

    // 从过滤的节点中收集布局ID
    const collectLayoutIds = (nodes) => {
        if (!Array.isArray(nodes)) return;

        nodes.forEach(node => {
            if (node.layout) {
                layoutIds.add(node.layout);
            }
            if (node.children) {
                collectLayoutIds(node.children);
            }
        });
    };

    collectLayoutIds(filteredData.nodes);

    // 收集布局数据
    layoutIds.forEach(layoutId => {
        if (figmaData[layoutId]) {
            filteredData.layouts[layoutId] = figmaData[layoutId];
        }
    });
}

/**
 * 收集关联的样式数据
 */
function collectRelatedStyles(figmaData, filteredData) {
    const styleIds = new Set();
    const fillIds = new Set();
    const strokeIds = new Set();

    // 从过滤的节点中收集样式ID
    const collectStyleIds = (nodes) => {
        if (!Array.isArray(nodes)) return;

        nodes.forEach(node => {
            if (node.fills) {
                if (typeof node.fills === 'string') {
                    fillIds.add(node.fills);
                }
            }
            if (node.strokes) {
                if (typeof node.strokes === 'string') {
                    strokeIds.add(node.strokes);
                }
            }
            if (node.children) {
                collectStyleIds(node.children);
            }
        });
    };

    collectStyleIds(filteredData.nodes);

    // 收集样式数据
    fillIds.forEach(fillId => {
        if (figmaData[fillId]) {
            filteredData.fills[fillId] = figmaData[fillId];
        }
    });

    strokeIds.forEach(strokeId => {
        if (figmaData[strokeId]) {
            filteredData.strokes[strokeId] = figmaData[strokeId];
        }
    });
}

/**
 * 检查是否匹配关键词
 */
function matchesKeywords(text, keywords) {
    if (!text || !Array.isArray(keywords) || keywords.length === 0) {
        return false;
    }

    const lowerText = text.toLowerCase();
    return keywords.some(keyword =>
        lowerText.includes(keyword.toLowerCase())
    );
}

/**
 * 获取过滤统计信息
 */
function getFilterStats(filteredData) {
    return {
        components: Object.keys(filteredData.metadata.components).length,
        componentSets: Object.keys(filteredData.metadata.componentSets).length,
        nodes: countNodes(filteredData.nodes),
        layouts: Object.keys(filteredData.layouts).length,
        fills: Object.keys(filteredData.fills).length,
        strokes: Object.keys(filteredData.strokes).length
    };
}

/**
 * 递归计算节点数量
 */
function countNodes(nodes) {
    if (!Array.isArray(nodes)) return 0;

    let count = nodes.length;
    nodes.forEach(node => {
        if (node.children) {
            count += countNodes(node.children);
        }
    });
    return count;
}

/**
 * 计算数据大小（估算）
 */
function getDataSize(figmaData, filteredData) {
    const originalSize = JSON.stringify(figmaData).length;
    const filteredSize = JSON.stringify(filteredData).length;
    const reduction = ((originalSize - filteredSize) / originalSize * 100).toFixed(2);

    return {
        original: originalSize,
        filtered: filteredSize,
        reduction: `${reduction}%`
    };
}

/**
 * 函数调用方式：过滤 Figma 数据
 * @param {string[]} keywords - 关键词数组
 * @param {Object} options - 选项配置
 * @param {string} options.figmaFilePath - Figma 文件路径，默认 './figma.yml'
 * @param {boolean} options.saveToFile - 是否保存到文件，默认 false
 * @param {string} options.outputPath - 输出文件路径，默认自动生成
 * @param {boolean} options.verbose - 是否显示详细信息，默认 false
 * @returns {Object} 过滤后的数据和统计信息
 */
function filterFigmaData(keywords, options = {}) {
    const {
        figmaFilePath = './figma.yml',
        saveToFile = false,
        outputPath = null,
        verbose = false
    } = options;

    if (!Array.isArray(keywords) || keywords.length === 0) {
        throw new Error('关键词参数必须是非空数组');
    }

    try {
        const filter = new FigmaDataFilter(figmaFilePath);
        const filteredData = filter.filterByKeywords(keywords);

        // 获取统计信息
        const stats = filter.getFilterStats();
        const sizeInfo = filter.getDataSize();

        const result = {
            success: true,
            keywords: keywords,
            data: filteredData,
            stats: stats,
            sizeInfo: sizeInfo,
            timestamp: new Date().toISOString()
        };

        if (verbose) {
            console.log('=== 过滤结果统计 ===');
            console.log(`关键词: ${keywords.join(', ')}`);
            console.log(`组件: ${stats.components}`);
            console.log(`组件集: ${stats.componentSets}`);
            console.log(`节点: ${stats.nodes}`);
            console.log(`布局: ${stats.layouts}`);
            console.log(`数据减少: ${sizeInfo.reduction}`);
        }

        // 保存文件（可选）
        if (saveToFile) {
            const filename = outputPath || `filtered_data_${keywords.join('_').replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')}.json`;
            fs.writeFileSync(filename, JSON.stringify(result, null, 2), 'utf8');
            result.savedTo = filename;

            if (verbose) {
                console.log(`✅ 数据已保存到: ${filename}`);
            }
        }

        return result;

    } catch (error) {
        return {
            success: false,
            error: error.message,
            keywords: keywords,
            timestamp: new Date().toISOString()
        };
    }
}

/**
 * 快速过滤按钮数据的便捷函数
 * @param {string[]} additionalKeywords - 额外的关键词，默认会包含 ['按钮', 'Button']
 * @param {Object} options - 选项配置
 * @returns {Object} 过滤后的按钮数据
 */
function filterButtonData(additionalKeywords = [], options = {}) {
    const buttonKeywords = ['按钮', 'Button', ...additionalKeywords];
    return filterFigmaData(buttonKeywords, options);
}

/**
 * 按尺寸过滤按钮数据
 * @param {string} size - 尺寸类型: 'small', 'medium', 'large' 或 '小', '中', '大'
 * @param {Object} options - 选项配置
 * @returns {Object} 过滤后的特定尺寸按钮数据
 */
function filterButtonsBySize(size, options = {}) {
    const sizeMap = {
        'small': ['小按钮', 'small'],
        'medium': ['中按钮', 'medium'],
        'large': ['大按钮', 'large'],
        '小': ['小按钮', 'small'],
        '中': ['中按钮', 'medium'],
        '大': ['大按钮', 'large']
    };

    const sizeKeywords = sizeMap[size];
    if (!sizeKeywords) {
        throw new Error(`不支持的尺寸类型: ${size}。支持的类型: small, medium, large, 小, 中, 大`);
    }

    return filterButtonData(sizeKeywords, options);
}

/**
 * 获取按钮尺寸摘要（为 LLM 分析优化）
 * @param {string[]} keywords - 关键词数组，默认 ['按钮', 'Button']
 * @param {Object} options - 选项配置
 * @returns {Object} 按钮尺寸摘要数据
 */
function getButtonDimensionsSummary(keywords = ['按钮', 'Button'], options = {}) {
    const result = filterFigmaData(keywords, options);

    if (!result.success) {
        return result;
    }

    const summary = {
        success: true,
        keywords: keywords,
        dimensions: [],
        grouped: {
            small: [],    // ≤24px
            medium: [],   // 25-35px
            large: []     // >35px
        },
        stats: {
            totalDimensions: 0,
            uniqueSizes: new Set(),
            sizeDistribution: {}
        },
        timestamp: new Date().toISOString()
    };

    // 提取尺寸信息
    Object.entries(result.data.layouts).forEach(([layoutId, layout]) => {
        if (layout.dimensions) {
            const { width, height } = layout.dimensions;
            const sizeKey = `${width}x${height}`;

            const dimInfo = {
                layoutId,
                width,
                height,
                size: sizeKey
            };

            summary.dimensions.push(dimInfo);
            summary.stats.uniqueSizes.add(sizeKey);
            summary.stats.sizeDistribution[sizeKey] = (summary.stats.sizeDistribution[sizeKey] || 0) + 1;

            // 按高度分组
            if (height <= 24) {
                summary.grouped.small.push(dimInfo);
            } else if (height <= 35) {
                summary.grouped.medium.push(dimInfo);
            } else {
                summary.grouped.large.push(dimInfo);
            }
        }
    });

    summary.stats.totalDimensions = summary.dimensions.length;
    summary.stats.uniqueSizes = Array.from(summary.stats.uniqueSizes);

    return summary;
}

/**
 * 主函数（保持命令行兼容性）
 */
function main() {
    const args = process.argv.slice(2);

    if (args.length === 0) {
        console.log('用法: node filterFigmaData.js <关键词1> [关键词2] [关键词3] ...');
        console.log('示例: node filterFigmaData.js 按钮 Button 小按钮');
        console.log('\n或者在代码中使用函数调用:');
        console.log('const { filterFigmaData } = require("./filterFigmaData");');
        console.log('const result = filterFigmaData(["按钮", "Button"]);');
        process.exit(1);
    }

    const keywords = args;
    console.log('=== Figma 数据过滤器 ===\n');

    const result = filterFigmaData(keywords, {
        verbose: true,
        saveToFile: true
    });

    if (!result.success) {
        console.error('过滤数据时发生错误:', result.error);
        process.exit(1);
    }
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
    main();
}

// 导出所有函数和类供其他模块使用
module.exports = {
    FigmaDataFilter,
    filterFigmaData,
    filterButtonData,
    filterButtonsBySize,
    getButtonDimensionsSummary
};
