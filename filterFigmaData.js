#!/usr/bin/env node

const fs = require('fs');
const yaml = require('js-yaml');

/**
 * 根据关键词过滤 Figma 数据，保留关联数据
 */
class FigmaDataFilter {
    constructor(figmaFilePath = './figma.yml') {
        this.figmaData = this.loadFigmaData(figmaFilePath);
        this.filteredData = {
            metadata: { components: {}, componentSets: {} },
            nodes: [],
            layouts: {},
            styles: {},
            fills: {},
            strokes: {}
        };
    }

    loadFigmaData(filePath) {
        try {
            if (!fs.existsSync(filePath)) {
                throw new Error(`文件不存在: ${filePath}`);
            }
            const fileContent = fs.readFileSync(filePath, 'utf8');
            return yaml.load(fileContent);
        } catch (error) {
            throw new Error(`加载 Figma 数据失败: ${error.message}`);
        }
    }

    /**
     * 根据关键词过滤数据
     */
    filterByKeywords(keywords) {
        console.log(`开始过滤，关键词: ${keywords.join(', ')}`);
        
        // 重置过滤结果
        this.resetFilteredData();
        
        // 1. 过滤组件和组件集
        this.filterComponents(keywords);
        
        // 2. 过滤节点
        this.filterNodes(keywords);
        
        // 3. 收集关联的布局数据
        this.collectRelatedLayouts();
        
        // 4. 收集关联的样式数据
        this.collectRelatedStyles();
        
        // 5. 统计过滤结果
        const stats = this.getFilterStats();
        console.log('过滤完成:', stats);
        
        return this.filteredData;
    }

    resetFilteredData() {
        this.filteredData = {
            metadata: { components: {}, componentSets: {} },
            nodes: [],
            layouts: {},
            styles: {},
            fills: {},
            strokes: {}
        };
    }

    /**
     * 过滤组件和组件集
     */
    filterComponents(keywords) {
        // 过滤组件
        const components = this.figmaData.metadata?.components || {};
        Object.entries(components).forEach(([id, component]) => {
            if (this.matchesKeywords(component.name, keywords)) {
                this.filteredData.metadata.components[id] = component;
            }
        });

        // 过滤组件集
        const componentSets = this.figmaData.metadata?.componentSets || {};
        Object.entries(componentSets).forEach(([id, componentSet]) => {
            if (this.matchesKeywords(componentSet.name, keywords)) {
                this.filteredData.metadata.componentSets[id] = componentSet;
            }
        });

        // 如果找到了组件集，也要包含其下的所有组件
        Object.keys(this.filteredData.metadata.componentSets).forEach(setId => {
            Object.entries(components).forEach(([compId, comp]) => {
                if (comp.componentSetId === setId) {
                    this.filteredData.metadata.components[compId] = comp;
                }
            });
        });
    }

    /**
     * 过滤节点
     */
    filterNodes(keywords) {
        const filteredNodeIds = new Set();
        
        const filterNodesRecursive = (nodes) => {
            if (!Array.isArray(nodes)) return [];
            
            const filtered = [];
            nodes.forEach(node => {
                let shouldInclude = false;
                
                // 检查节点名称是否匹配
                if (node.name && this.matchesKeywords(node.name, keywords)) {
                    shouldInclude = true;
                }
                
                // 检查是否使用了匹配的组件
                if (node.componentId && this.filteredData.metadata.components[node.componentId]) {
                    shouldInclude = true;
                }
                
                // 递归处理子节点
                let filteredChildren = [];
                if (node.children) {
                    filteredChildren = filterNodesRecursive(node.children);
                    if (filteredChildren.length > 0) {
                        shouldInclude = true;
                    }
                }
                
                if (shouldInclude) {
                    const filteredNode = { ...node };
                    if (filteredChildren.length > 0) {
                        filteredNode.children = filteredChildren;
                    }
                    filtered.push(filteredNode);
                    filteredNodeIds.add(node.id);
                }
            });
            
            return filtered;
        };

        if (this.figmaData.nodes) {
            this.filteredData.nodes = filterNodesRecursive(this.figmaData.nodes);
        }
        
        return filteredNodeIds;
    }

    /**
     * 收集关联的布局数据
     */
    collectRelatedLayouts() {
        const layoutIds = new Set();
        
        // 从过滤的节点中收集布局ID
        const collectLayoutIds = (nodes) => {
            if (!Array.isArray(nodes)) return;
            
            nodes.forEach(node => {
                if (node.layout) {
                    layoutIds.add(node.layout);
                }
                if (node.children) {
                    collectLayoutIds(node.children);
                }
            });
        };
        
        collectLayoutIds(this.filteredData.nodes);
        
        // 收集布局数据
        layoutIds.forEach(layoutId => {
            if (this.figmaData[layoutId]) {
                this.filteredData.layouts[layoutId] = this.figmaData[layoutId];
            }
        });
    }

    /**
     * 收集关联的样式数据
     */
    collectRelatedStyles() {
        const styleIds = new Set();
        const fillIds = new Set();
        const strokeIds = new Set();
        
        // 从过滤的节点中收集样式ID
        const collectStyleIds = (nodes) => {
            if (!Array.isArray(nodes)) return;
            
            nodes.forEach(node => {
                if (node.fills) {
                    if (typeof node.fills === 'string') {
                        fillIds.add(node.fills);
                    }
                }
                if (node.strokes) {
                    if (typeof node.strokes === 'string') {
                        strokeIds.add(node.strokes);
                    }
                }
                if (node.children) {
                    collectStyleIds(node.children);
                }
            });
        };
        
        collectStyleIds(this.filteredData.nodes);
        
        // 收集样式数据
        fillIds.forEach(fillId => {
            if (this.figmaData[fillId]) {
                this.filteredData.fills[fillId] = this.figmaData[fillId];
            }
        });
        
        strokeIds.forEach(strokeId => {
            if (this.figmaData[strokeId]) {
                this.filteredData.strokes[strokeId] = this.figmaData[strokeId];
            }
        });
    }

    /**
     * 检查是否匹配关键词
     */
    matchesKeywords(text, keywords) {
        if (!text || !Array.isArray(keywords) || keywords.length === 0) {
            return false;
        }
        
        const lowerText = text.toLowerCase();
        return keywords.some(keyword => 
            lowerText.includes(keyword.toLowerCase())
        );
    }

    /**
     * 获取过滤统计信息
     */
    getFilterStats() {
        return {
            components: Object.keys(this.filteredData.metadata.components).length,
            componentSets: Object.keys(this.filteredData.metadata.componentSets).length,
            nodes: this.countNodes(this.filteredData.nodes),
            layouts: Object.keys(this.filteredData.layouts).length,
            fills: Object.keys(this.filteredData.fills).length,
            strokes: Object.keys(this.filteredData.strokes).length
        };
    }

    /**
     * 递归计算节点数量
     */
    countNodes(nodes) {
        if (!Array.isArray(nodes)) return 0;
        
        let count = nodes.length;
        nodes.forEach(node => {
            if (node.children) {
                count += this.countNodes(node.children);
            }
        });
        return count;
    }

    /**
     * 计算数据大小（估算）
     */
    getDataSize() {
        const originalSize = JSON.stringify(this.figmaData).length;
        const filteredSize = JSON.stringify(this.filteredData).length;
        const reduction = ((originalSize - filteredSize) / originalSize * 100).toFixed(2);
        
        return {
            original: originalSize,
            filtered: filteredSize,
            reduction: `${reduction}%`
        };
    }
}

/**
 * 主函数
 */
function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('用法: node filterFigmaData.js <关键词1> [关键词2] [关键词3] ...');
        console.log('示例: node filterFigmaData.js 按钮 Button 小按钮');
        process.exit(1);
    }

    const keywords = args;
    console.log('=== Figma 数据过滤器 ===\n');
    
    try {
        const filter = new FigmaDataFilter();
        const filteredData = filter.filterByKeywords(keywords);
        
        // 输出统计信息
        const stats = filter.getFilterStats();
        const sizeInfo = filter.getDataSize();
        
        console.log('\n=== 过滤结果统计 ===');
        console.log(`组件: ${stats.components}`);
        console.log(`组件集: ${stats.componentSets}`);
        console.log(`节点: ${stats.nodes}`);
        console.log(`布局: ${stats.layouts}`);
        console.log(`填充样式: ${stats.fills}`);
        console.log(`描边样式: ${stats.strokes}`);
        
        console.log('\n=== 数据大小 ===');
        console.log(`原始大小: ${sizeInfo.original} 字符`);
        console.log(`过滤后大小: ${sizeInfo.filtered} 字符`);
        console.log(`减少: ${sizeInfo.reduction}`);
        
        // 保存过滤后的数据
        const outputFile = `filtered_data_${keywords.join('_').replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')}.json`;
        fs.writeFileSync(outputFile, JSON.stringify(filteredData, null, 2), 'utf8');
        console.log(`\n✅ 过滤后的数据已保存到: ${outputFile}`);
        
    } catch (error) {
        console.error('过滤数据时发生错误:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
    main();
}

// 导出类供其他模块使用
module.exports = { FigmaDataFilter };
