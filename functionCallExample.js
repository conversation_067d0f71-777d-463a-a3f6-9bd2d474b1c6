#!/usr/bin/env node

const { 
    filterFigmaData, 
    filterButtonData, 
    filterButtonsBySize, 
    getButtonDimensionsSummary 
} = require('./filterFigmaData');

/**
 * 函数调用方式使用示例
 */
async function demonstrateFunctionCalls() {
    console.log('=== Figma 数据过滤器 - 函数调用方式 ===\n');

    try {
        // 示例1: 基础函数调用
        console.log('1. 基础函数调用 - 过滤按钮数据');
        const result1 = filterFigmaData(['按钮', 'Button'], {
            verbose: true
        });
        
        if (result1.success) {
            console.log(`✅ 成功过滤，找到 ${result1.stats.components} 个组件`);
            console.log(`数据减少: ${result1.sizeInfo.reduction}\n`);
        } else {
            console.log(`❌ 过滤失败: ${result1.error}\n`);
        }

        // 示例2: 便捷的按钮过滤函数
        console.log('2. 便捷按钮过滤函数');
        const buttonResult = filterButtonData(['小'], {
            verbose: false
        });
        
        if (buttonResult.success) {
            console.log(`✅ 找到 ${buttonResult.stats.components} 个按钮组件`);
            console.log(`布局数据: ${buttonResult.stats.layouts} 个\n`);
        }

        // 示例3: 按尺寸过滤按钮
        console.log('3. 按尺寸过滤按钮');
        const smallButtons = filterButtonsBySize('small', {
            verbose: false
        });
        
        if (smallButtons.success) {
            console.log(`✅ 小按钮数据:`);
            console.log(`- 组件: ${smallButtons.stats.components} 个`);
            console.log(`- 布局: ${smallButtons.stats.layouts} 个`);
            
            // 显示具体尺寸
            Object.entries(smallButtons.data.layouts).forEach(([layoutId, layout]) => {
                if (layout.dimensions) {
                    console.log(`- ${layoutId}: ${layout.dimensions.width}px × ${layout.dimensions.height}px`);
                }
            });
            console.log();
        }

        // 示例4: 获取按钮尺寸摘要（LLM 优化格式）
        console.log('4. 按钮尺寸摘要（LLM 优化）');
        const summary = getButtonDimensionsSummary(['按钮', 'Button']);
        
        if (summary.success) {
            console.log(`✅ 尺寸摘要:`);
            console.log(`- 总尺寸数: ${summary.stats.totalDimensions}`);
            console.log(`- 唯一尺寸: ${summary.stats.uniqueSizes.length} 种`);
            console.log(`- 小按钮: ${summary.grouped.small.length} 个`);
            console.log(`- 中按钮: ${summary.grouped.medium.length} 个`);
            console.log(`- 大按钮: ${summary.grouped.large.length} 个`);
            
            console.log('\n所有唯一尺寸:');
            summary.stats.uniqueSizes.forEach(size => {
                const count = summary.stats.sizeDistribution[size];
                console.log(`  ${size}: ${count} 个`);
            });
            console.log();
        }

        // 示例5: 保存数据到文件
        console.log('5. 保存数据到文件');
        const savedResult = filterFigmaData(['按钮', '实色'], {
            saveToFile: true,
            outputPath: 'button_solid_data.json',
            verbose: false
        });
        
        if (savedResult.success && savedResult.savedTo) {
            console.log(`✅ 数据已保存到: ${savedResult.savedTo}\n`);
        }

        // 示例6: 错误处理
        console.log('6. 错误处理示例');
        const errorResult = filterFigmaData([], {}); // 空关键词数组
        if (!errorResult.success) {
            console.log(`❌ 预期的错误: ${errorResult.error}\n`);
        }

        // 示例7: 链式调用和数据处理
        console.log('7. 数据处理和分析');
        const analysisResult = analyzeButtonDimensions();
        console.log(analysisResult);

    } catch (error) {
        console.error('示例运行失败:', error.message);
    }
}

/**
 * 分析按钮尺寸的自定义函数
 */
function analyzeButtonDimensions() {
    // 获取所有按钮数据
    const allButtons = filterButtonData();
    
    if (!allButtons.success) {
        return { error: '无法获取按钮数据' };
    }

    // 分析尺寸分布
    const dimensionAnalysis = {
        totalLayouts: Object.keys(allButtons.data.layouts).length,
        dimensions: [],
        heightDistribution: {},
        widthDistribution: {},
        recommendations: []
    };

    Object.entries(allButtons.data.layouts).forEach(([layoutId, layout]) => {
        if (layout.dimensions) {
            const { width, height } = layout.dimensions;
            
            dimensionAnalysis.dimensions.push({
                layoutId,
                width,
                height,
                aspectRatio: (width / height).toFixed(2)
            });

            // 统计高度分布
            dimensionAnalysis.heightDistribution[height] = 
                (dimensionAnalysis.heightDistribution[height] || 0) + 1;
            
            // 统计宽度分布
            dimensionAnalysis.widthDistribution[width] = 
                (dimensionAnalysis.widthDistribution[width] || 0) + 1;
        }
    });

    // 生成建议
    const heights = Object.keys(dimensionAnalysis.heightDistribution).map(Number).sort((a, b) => a - b);
    const widths = Object.keys(dimensionAnalysis.widthDistribution).map(Number).sort((a, b) => a - b);

    dimensionAnalysis.recommendations.push(
        `发现 ${heights.length} 种不同高度: ${heights.join(', ')}px`,
        `发现 ${widths.length} 种不同宽度: ${widths.join(', ')}px`,
        `最小高度: ${Math.min(...heights)}px，最大高度: ${Math.max(...heights)}px`,
        `最小宽度: ${Math.min(...widths)}px，最大宽度: ${Math.max(...widths)}px`
    );

    return dimensionAnalysis;
}

/**
 * 为 LLM 分析准备数据
 */
function prepareDataForLLM(keywords) {
    console.log('\n=== 为 LLM 分析准备数据 ===');
    
    // 获取摘要数据
    const summary = getButtonDimensionsSummary(keywords);
    
    if (!summary.success) {
        return { error: summary.error };
    }

    // 创建 LLM 友好的数据格式
    const llmData = {
        query: `分析关键词 "${keywords.join(', ')}" 相关的按钮尺寸`,
        data: {
            totalDimensions: summary.stats.totalDimensions,
            uniqueSizes: summary.stats.uniqueSizes,
            sizeGroups: {
                small: summary.grouped.small.map(d => `${d.width}x${d.height}`),
                medium: summary.grouped.medium.map(d => `${d.width}x${d.height}`),
                large: summary.grouped.large.map(d => `${d.width}x${d.height}`)
            },
            allDimensions: summary.dimensions.map(d => ({
                size: d.size,
                width: d.width,
                height: d.height
            }))
        },
        prompt: `
请分析以上按钮尺寸数据，回答以下问题：
1. 按钮有哪些标准尺寸？
2. 高度和宽度的规律是什么？
3. 不同尺寸按钮的使用场景建议？
4. 是否符合设计系统的一致性原则？
        `.trim()
    };

    // 保存 LLM 数据
    const fs = require('fs');
    const filename = `llm_analysis_${keywords.join('_').replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')}.json`;
    fs.writeFileSync(filename, JSON.stringify(llmData, null, 2), 'utf8');
    
    console.log(`✅ LLM 分析数据已保存到: ${filename}`);
    console.log(`数据大小: ${JSON.stringify(llmData).length} 字符`);
    
    return llmData;
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--llm')) {
        // 为 LLM 准备数据
        const keywords = args.filter(arg => arg !== '--llm');
        if (keywords.length === 0) {
            keywords.push('按钮', 'Button');
        }
        prepareDataForLLM(keywords);
    } else {
        // 运行完整示例
        await demonstrateFunctionCalls();
    }
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
    main();
}

module.exports = { 
    demonstrateFunctionCalls, 
    analyzeButtonDimensions, 
    prepareDataForLLM 
};
