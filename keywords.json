{"extractedAt": "2025-08-13T12:18:23.205Z", "totalCount": 213, "keywords": [" ", "-", "00 系统/手势条 Home Indicator", "01 控件/按钮Button", "01 控件/按钮Button/小按钮/长/白/实色/正常", "01 控件/按钮Button·纯文字", "01 控件/按钮·纯文字/灰/无", "02 Bar/顶部栏 Top navbar·图片型", "04 状态/页面状态Page status", "Assets > bilibili UIkit > 02 组件 Components > 01 控件", "<PERSON><PERSON>", "Ellipse 3", "Frame 1", "Frame 103", "Frame 104", "Frame 183", "Frame 328", "Frame 33", "Frame 330", "Frame 331", "Frame 332", "Frame 34551782", "Frame 34551784", "Frame 34551794", "Frame 34551795", "Frame 34551796", "Frame 34551797", "Frame 34551798", "Frame 34551800", "Frame 34551801", "Frame 34551802", "Frame 34551803", "Frame 34551804", "Frame 34551805", "Frame 4", "Frame 54", "Group 34551744", "Group 34551745", "Group 34551749", "Group 34551750", "Line 7", "Rectangle 1100", "Rectangle 167", "Rectangle 168", "Rectangle 169", "Rectangle 174", "Rectangle 175", "Rectangle 176", "Rectangle 177", "Rectangle 2214", "Rectangle 2215", "Rectangle 35", "Rectangle 453", "Vector 1", "Vector 2", "Vector 474", "Vector 489", "Vector 490", "Vector 502", "Vector 503", "Vector 504", "Vector 505", "Vector 507", "Vector 508", "Vector 509", "iPhone 11 Pro / X - 3", "iPhone 11 Pro / X - 4", "iPhone 11 Pro / X - 5", "image 13", "image 14", "image 23", "image 29", "image 30", "image 31", "image 9", "万圣节尖叫之夜", "不同操作类型有不同颜色的按钮来区分", "不同类型与变体", "个人空间", "中", "中按钮", "中按钮：若空间足够推荐使用该尺寸", "互粉态", "以关注场景为例，介绍类似场景使用规范", "使用场景", "关注、追番和收藏等场景", "关注态", "关注模块", "内容", "功能布局=右侧三图标", "动态、评论、历史记录", "变体设置", "右侧图标", "图标", "大按钮", "大按钮：页面级别的CTA按钮", "实色", "实色、线框、浅底，依次视觉强度和优先级递减", "小按钮", "小按钮：较小，比较常用，适用于空间较小的场景", "尺寸", "尺寸=中按钮, 推荐宽度=中, 颜色=白, 样式=实色, 状态=正常", "尺寸=中按钮, 推荐宽度=中, 颜色=粉(默认), 样式=实色, 状态=正常", "尺寸=中按钮, 推荐宽度=短, 颜色=白, 样式=实色, 状态=正常", "尺寸=中按钮, 推荐宽度=长, 颜色=灰, 样式=浅底, 状态=正常", "尺寸=中按钮, 推荐宽度=长, 颜色=白, 样式=实色, 状态=正常", "尺寸=中按钮, 推荐宽度=长, 颜色=粉(默认), 样式=实色, 状态=正常", "尺寸=大按钮, 推荐宽度=中, 颜色=白, 样式=实色, 状态=正常", "尺寸=大按钮, 推荐宽度=中, 颜色=粉(默认), 样式=实色, 状态=正常", "尺寸=大按钮, 推荐宽度=长, 颜色=白, 样式=实色, 状态=正常", "尺寸=大按钮, 推荐宽度=长, 颜色=粉(默认), 样式=实色, 状态=正常", "尺寸=小按钮, 推荐宽度=中, 颜色=灰, 样式=浅底, 状态=正常", "尺寸=小按钮, 推荐宽度=中, 颜色=白, 样式=实色, 状态=正常", "尺寸=小按钮, 推荐宽度=中, 颜色=粉(默认), 样式=实色, 状态=按下", "尺寸=小按钮, 推荐宽度=中, 颜色=粉(默认), 样式=实色, 状态=正常", "尺寸=小按钮, 推荐宽度=中, 颜色=粉(默认), 样式=实色, 状态=禁用", "尺寸=小按钮, 推荐宽度=中, 颜色=粉(默认), 样式=浅底, 状态=正常", "尺寸=小按钮, 推荐宽度=中, 颜色=粉(默认), 样式=线框, 状态=正常", "尺寸=小按钮, 推荐宽度=短, 颜色=灰, 样式=浅底, 状态=正常", "尺寸=小按钮, 推荐宽度=短, 颜色=白, 样式=实色, 状态=正常", "尺寸=小按钮, 推荐宽度=短, 颜色=粉(默认), 样式=实色, 状态=正常", "尺寸=小按钮, 推荐宽度=短, 颜色=蓝(下载、游戏等), 样式=实色, 状态=正常", "尺寸=小按钮, 推荐宽度=短, 颜色=黄(付费通知), 样式=实色, 状态=正常", "尺寸=小按钮, 推荐宽度=长, 颜色=粉(默认), 样式=实色, 状态=正常", "左侧图标", "布局", "常规", "序号", "弱", "强关注", "待补充", "我的关注", "投稿赢千元奖金，万圣节限量礼品", "按下", "按下：手指按下状态变化", "按钮", "按钮主要由文字、图标和背板组成", "按钮可配置图标以辅助和加强示意", "按钮在页面位置相对自由，列举一些典型场景", "按钮文字2", "按钮用于触发操作或提交表单数据，可供用户单击或触摸，具有文本标签或图标以表示其目的", "按钮的尺寸参数", "推荐宽度", "提示栏图片", "播放页、直播、专栏、搜索", "文字内容", "文字按钮", "文字按钮：引导性很弱的时候使用", "方向=none, 样式=填充", "方向=┗┫, 背景=off, 状态=默认说明", "方向=┣, 样式=填充", "方向=┣, 背景=off", "方向=┣, 背景=off, 状态=默认说明", "方向=┣┓, 背景=off, 状态=默认说明", "方向=┫, 背景=off", "方向=┳, 背景=off", "方向=┻, 样式=填充", "方向=┻, 背景=off", "无图标", "普通按钮", "普通按钮：常用类型", "有图标", "标注/", "标注/文字数字指引", "标题", "样式", "概述", "正常", "正常态", "正常：默认正常状态", "浅底", "灰色", "灰色：弱化和不积极的情况", "热门、排行榜、推荐关注、动态活动", "特别关注", "状态", "白色", "白色：彩色或图片背景", "短", "禁用", "禁用：不可点击状态", "类型", "类型=UP主", "类型=占位", "类型=容器", "类型=竖屏, 深浅色=light", "类型=间距上下", "类型=间距左右", "类型=默认, 深色模式=false", "类型与变体", "粉丝态", "粉色", "粉色：默认常用", "线框", "组件选项对应关系", "结构", "背板", "蓝色", "蓝色：下载、游戏类", "规格", "规范用例", "详情", "说明", "调用说明", "调用路径", "这里是某个人编辑写的个人简介", "遮罩", "长", "预设了几档默认宽度，尽可能规范使用，必要时也可拖动以自定义宽度", "颜色", "颜色=粉（默认）, 状态=正常", "黄色", "黄色：付费类"]}