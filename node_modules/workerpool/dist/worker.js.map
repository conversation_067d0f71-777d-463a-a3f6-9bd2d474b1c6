{"version": 3, "file": "worker.js", "sources": ["../src/transfer.js", "../src/Promise.js", "../src/worker.js"], "sourcesContent": ["/**\n * The helper class for transferring data from the worker to the main thread.\n *\n * @param {Object} message The object to deliver to the main thread.\n * @param {Object[]} transfer An array of transferable Objects to transfer ownership of.\n */\nfunction Transfer(message, transfer) {\n  this.message = message;\n  this.transfer = transfer;\n}\n\nmodule.exports = Transfer;\n", "'use strict';\n\n/**\n * Promise\n *\n * Inspired by https://gist.github.com/RubaXa/8501359 from RubaXa <<EMAIL>>\n * @template T\n * @template [E=Error]\n * @param {Function} handler   Called as handler(resolve: Function, reject: Function)\n * @param {Promise} [parent]   Parent promise for propagation of cancel and timeout\n */\nfunction Promise(handler, parent) {\n  var me = this;\n\n  if (!(this instanceof Promise)) {\n    throw new SyntaxError('Constructor must be called with the new operator');\n  }\n\n  if (typeof handler !== 'function') {\n    throw new SyntaxError('Function parameter handler(resolve, reject) missing');\n  }\n\n  var _onSuccess = [];\n  var _onFail = [];\n\n  // status\n  /**\n   * @readonly\n   */\n  this.resolved = false;\n  /**\n   * @readonly\n   */\n  this.rejected = false;\n  /**\n   * @readonly\n   */\n  this.pending = true;\n  /**\n   * @readonly\n   */\n  this[Symbol.toStringTag] = 'Promise';\n\n  /**\n   * Process onSuccess and onFail callbacks: add them to the queue.\n   * Once the promise is resolved, the function _promise is replace.\n   * @param {Function} onSuccess\n   * @param {Function} onFail\n   * @private\n   */\n  var _process = function (onSuccess, onFail) {\n    _onSuccess.push(onSuccess);\n    _onFail.push(onFail);\n  };\n\n  /**\n   * Add an onSuccess callback and optionally an onFail callback to the Promise\n   * @template TT\n   * @template [TE=never]\n   * @param {(r: T) => TT | PromiseLike<TT>} onSuccess\n   * @param {(r: E) => TE | PromiseLike<TE>} [onFail]\n   * @returns {Promise<TT | TE, any>} promise\n   */\n  this.then = function (onSuccess, onFail) {\n    return new Promise(function (resolve, reject) {\n      var s = onSuccess ? _then(onSuccess, resolve, reject) : resolve;\n      var f = onFail    ? _then(onFail,    resolve, reject) : reject;\n\n      _process(s, f);\n    }, me);\n  };\n\n  /**\n   * Resolve the promise\n   * @param {*} result\n   * @type {Function}\n   */\n  var _resolve = function (result) {\n    // update status\n    me.resolved = true;\n    me.rejected = false;\n    me.pending = false;\n\n    _onSuccess.forEach(function (fn) {\n      fn(result);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onSuccess(result);\n    };\n\n    _resolve = _reject = function () { };\n\n    return me;\n  };\n\n  /**\n   * Reject the promise\n   * @param {Error} error\n   * @type {Function}\n   */\n  var _reject = function (error) {\n    // update status\n    me.resolved = false;\n    me.rejected = true;\n    me.pending = false;\n\n    _onFail.forEach(function (fn) {\n      fn(error);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onFail(error);\n    };\n\n    _resolve = _reject = function () { }\n\n    return me;\n  };\n\n  /**\n   * Cancel the promise. This will reject the promise with a CancellationError\n   * @returns {this} self\n   */\n  this.cancel = function () {\n    if (parent) {\n      parent.cancel();\n    }\n    else {\n      _reject(new CancellationError());\n    }\n\n    return me;\n  };\n\n  /**\n   * Set a timeout for the promise. If the promise is not resolved within\n   * the time, the promise will be cancelled and a TimeoutError is thrown.\n   * If the promise is resolved in time, the timeout is removed.\n   * @param {number} delay     Delay in milliseconds\n   * @returns {this} self\n   */\n  this.timeout = function (delay) {\n    if (parent) {\n      parent.timeout(delay);\n    }\n    else {\n      var timer = setTimeout(function () {\n        _reject(new TimeoutError('Promise timed out after ' + delay + ' ms'));\n      }, delay);\n\n      me.always(function () {\n        clearTimeout(timer);\n      });\n    }\n\n    return me;\n  };\n\n  // attach handler passing the resolve and reject functions\n  handler(function (result) {\n    _resolve(result);\n  }, function (error) {\n    _reject(error);\n  });\n}\n\n/**\n * Execute given callback, then call resolve/reject based on the returned result\n * @param {Function} callback\n * @param {Function} resolve\n * @param {Function} reject\n * @returns {Function}\n * @private\n */\nfunction _then(callback, resolve, reject) {\n  return function (result) {\n    try {\n      var res = callback(result);\n      if (res && typeof res.then === 'function' && typeof res['catch'] === 'function') {\n        // method returned a promise\n        res.then(resolve, reject);\n      }\n      else {\n        resolve(res);\n      }\n    }\n    catch (error) {\n      reject(error);\n    }\n  }\n}\n\n/**\n * Add an onFail callback to the Promise\n * @template TT\n * @param {(error: E) => TT | PromiseLike<TT>} onFail\n * @returns {Promise<T | TT>} promise\n */\nPromise.prototype['catch'] = function (onFail) {\n  return this.then(null, onFail);\n};\n\n// TODO: add support for Promise.catch(Error, callback)\n// TODO: add support for Promise.catch(Error, Error, callback)\n\n/**\n * Execute given callback when the promise either resolves or rejects.\n * @template TT\n * @param {() => Promise<TT>} fn\n * @returns {Promise<TT>} promise\n */\nPromise.prototype.always = function (fn) {\n  return this.then(fn, fn);\n};\n\n/**\n  * Execute given callback when the promise either resolves or rejects.\n  * Same semantics as Node's Promise.finally()\n  * @param {Function | null | undefined} [fn]\n  * @returns {Promise} promise\n  */\nPromise.prototype.finally = function (fn) {\n  const me = this;\n\n  const final = function() {\n    return new Promise((resolve) => resolve())\n      .then(fn)\n      .then(() => me);\n  };\n\n  return this.then(final, final);\n}\n\n/**\n * Create a promise which resolves when all provided promises are resolved,\n * and fails when any of the promises resolves.\n * @param {Promise[]} promises\n * @returns {Promise<any[], any>} promise\n */\nPromise.all = function (promises){\n  return new Promise(function (resolve, reject) {\n    var remaining = promises.length,\n        results = [];\n\n    if (remaining) {\n      promises.forEach(function (p, i) {\n        p.then(function (result) {\n          results[i] = result;\n          remaining--;\n          if (remaining == 0) {\n            resolve(results);\n          }\n        }, function (error) {\n          remaining = 0;\n          reject(error);\n        });\n      });\n    }\n    else {\n      resolve(results);\n    }\n  });\n};\n\n/**\n * Create a promise resolver\n * @returns {{promise: Promise, resolve: Function, reject: Function}} resolver\n */\nPromise.defer = function () {\n  var resolver = {};\n\n  resolver.promise = new Promise(function (resolve, reject) {\n    resolver.resolve = resolve;\n    resolver.reject = reject;\n  });\n\n  return resolver;\n};\n\n/**\n * Create a cancellation error\n * @param {String} [message]\n * @extends Error\n */\nfunction CancellationError(message) {\n  this.message = message || 'promise cancelled';\n  this.stack = (new Error()).stack;\n}\n\nCancellationError.prototype = new Error();\nCancellationError.prototype.constructor = Error;\nCancellationError.prototype.name = 'CancellationError';\n\nPromise.CancellationError = CancellationError;\n\n\n/**\n * Create a timeout error\n * @param {String} [message]\n * @extends Error\n */\nfunction TimeoutError(message) {\n  this.message = message || 'timeout exceeded';\n  this.stack = (new Error()).stack;\n}\n\nTimeoutError.prototype = new Error();\nTimeoutError.prototype.constructor = Error;\nTimeoutError.prototype.name = 'TimeoutError';\n\nPromise.TimeoutError = TimeoutError;\n\n\nexports.Promise = Promise;\n", "/**\n * worker must be started as a child process or a web worker.\n * It listens for RPC messages from the parent process.\n */\n\nvar Transfer = require('./transfer');\n\n/**\n * worker must handle async cleanup handlers. Use custom Promise implementation. \n*/\nvar Promise = require('./Promise').Promise;\n/**\n * Special message sent by parent which causes the worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\n/**\n * Special message by parent which causes a child process worker to perform cleaup\n * steps before determining if the child process worker should be terminated.\n*/\nvar CLEANUP_METHOD_ID = '__workerpool-cleanup__';\n// var nodeOSPlatform = require('./environment').nodeOSPlatform;\n\n\nvar TIMEOUT_DEFAULT = 1_000;\n\n// create a worker API for sending and receiving messages which works both on\n// node.js and in the browser\nvar worker = {\n  exit: function() {}\n};\n\n// api for in worker communication with parent process\n// works in both node.js and the browser\nvar publicWorker = {\n  /**\n   * Registers listeners which will trigger when a task is timed out or cancled. If all listeners resolve, the worker executing the given task will not be terminated.\n   * *Note*: If there is a blocking operation within a listener, the worker will be terminated.\n   * @param {() => Promise<void>} listener\n  */\n  addAbortListener: function(listener) {\n    worker.abortListeners.push(listener);\n  },\n\n  /**\n    * Emit an event from the worker thread to the main thread.\n    * @param {any} payload\n  */\n  emit: worker.emit\n};\n\nif (typeof self !== 'undefined' && typeof postMessage === 'function' && typeof addEventListener === 'function') {\n  // worker in the browser\n  worker.on = function (event, callback) {\n    addEventListener(event, function (message) {\n      callback(message.data);\n    })\n  };\n  worker.send = function (message, transfer) {\n     transfer ? postMessage(message, transfer) : postMessage (message);\n  };\n}\nelse if (typeof process !== 'undefined') {\n  // node.js\n\n  var WorkerThreads;\n  try {\n    WorkerThreads = require('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads, fallback to sub-process based workers\n    } else {\n      throw error;\n    }\n  }\n\n  if (WorkerThreads &&\n    /* if there is a parentPort, we are in a WorkerThread */\n    WorkerThreads.parentPort !== null) {\n    var parentPort  = WorkerThreads.parentPort;\n    worker.send = parentPort.postMessage.bind(parentPort);\n    worker.on = parentPort.on.bind(parentPort);\n    worker.exit = process.exit.bind(process);\n  } else {\n    worker.on = process.on.bind(process);\n    // ignore transfer argument since it is not supported by process\n    worker.send = function (message) {\n      process.send(message);\n    };\n    // register disconnect handler only for subprocess worker to exit when parent is killed unexpectedly\n    worker.on('disconnect', function () {\n      process.exit(1);\n    });\n    worker.exit = process.exit.bind(process);\n  }\n}\nelse {\n  throw new Error('Script must be executed as a worker');\n}\n\nfunction convertError(error) {\n  return Object.getOwnPropertyNames(error).reduce(function(product, name) {\n    return Object.defineProperty(product, name, {\n\tvalue: error[name],\n\tenumerable: true\n    });\n  }, {});\n}\n\n/**\n * Test whether a value is a Promise via duck typing.\n * @param {*} value\n * @returns {boolean} Returns true when given value is an object\n *                    having functions `then` and `catch`.\n */\nfunction isPromise(value) {\n  return value && (typeof value.then === 'function') && (typeof value.catch === 'function');\n}\n\n// functions available externally\nworker.methods = {};\n\n/**\n * Execute a function with provided arguments\n * @param {String} fn     Stringified function\n * @param {Array} [args]  Function arguments\n * @returns {*}\n */\nworker.methods.run = function run(fn, args) {\n  var f = new Function('return (' + fn + ').apply(this, arguments);');\n  f.worker = publicWorker;\n  return f.apply(f, args);\n};\n\n/**\n * Get a list with methods available on this worker\n * @return {String[]} methods\n */\nworker.methods.methods = function methods() {\n  return Object.keys(worker.methods);\n};\n\n/**\n * Custom handler for when the worker is terminated.\n */\nworker.terminationHandler = undefined;\n\nworker.abortListenerTimeout = TIMEOUT_DEFAULT;\n\n/**\n * Abort handlers for resolving errors which may cause a timeout or cancellation\n * to occur from a worker context\n */\nworker.abortListeners = [];\n\n/**\n * Cleanup and exit the worker.\n * @param {Number} code \n * @returns {Promise<void>}\n */\nworker.terminateAndExit = function(code) {\n  var _exit = function() {\n    worker.exit(code);\n  }\n\n  if(!worker.terminationHandler) {\n    return _exit();\n  }\n  \n  var result = worker.terminationHandler(code);\n  if (isPromise(result)) {\n    result.then(_exit, _exit);\n\n    return result;\n  } else {\n    _exit();\n    return new Promise(function (_resolve, reject) {\n      reject(new Error(\"Worker terminating\"));\n    });\n  }\n}\n\n\n\n/**\n  * Called within the worker message handler to run abort handlers if registered to perform cleanup operations.\n  * @param {Integer} [requestId] id of task which is currently executing in the worker\n  * @return {Promise<void>}\n*/\nworker.cleanup = function(requestId) {\n\n  if (!worker.abortListeners.length) {\n    worker.send({\n      id: requestId,\n      method: CLEANUP_METHOD_ID,\n      error: convertError(new Error('Worker terminating')),\n    });\n\n    // If there are no handlers registered, reject the promise with an error as we want the handler to be notified\n    // that cleanup should begin and the handler should be GCed.\n    return new Promise(function(resolve) { resolve(); });\n  }\n  \n\n  var _exit = function() {\n    worker.exit();\n  }\n\n  var _abort = function() {\n    if (!worker.abortListeners.length) {\n      worker.abortListeners = [];\n    }\n  }\n\n  const promises = worker.abortListeners.map(listener => listener());\n  let timerId;\n  const timeoutPromise = new Promise((_resolve, reject) => {\n    timerId = setTimeout(function () { \n      reject(new Error('Timeout occured waiting for abort handler, killing worker'));\n    }, worker.abortListenerTimeout);\n  });\n\n  // Once a promise settles we need to clear the timeout to prevet fulfulling the promise twice \n  const settlePromise = Promise.all(promises).then(function() {\n    clearTimeout(timerId);\n    _abort();\n  }, function() {\n    clearTimeout(timerId);\n    _exit();\n  });\n\n  // Returns a promise which will result in one of the following cases\n  // - Resolve once all handlers resolve\n  // - Reject if one or more handlers exceed the 'abortListenerTimeout' interval\n  // - Reject if one or more handlers reject\n  // Upon one of the above cases a message will be sent to the handler with the result of the handler execution\n  // which will either kill the worker if the result contains an error, or keep it in the pool if the result\n  // does not contain an error.\n  return new Promise(function(resolve, reject) {\n    settlePromise.then(resolve, reject);\n    timeoutPromise.then(resolve, reject);\n  }).then(function() {\n    worker.send({\n      id: requestId,\n      method: CLEANUP_METHOD_ID,\n      error: null,\n    });\n  }, function(err) {\n    worker.send({\n      id: requestId,\n      method: CLEANUP_METHOD_ID,\n      error: err ? convertError(err) : null,\n    });\n  });\n}\n\nvar currentRequestId = null;\n\nworker.on('message', function (request) {\n  if (request === TERMINATE_METHOD_ID) {\n    return worker.terminateAndExit(0);\n  }\n\n  if (request.method === CLEANUP_METHOD_ID) {\n    return worker.cleanup(request.id);\n  }\n\n  try {\n    var method = worker.methods[request.method];\n\n    if (method) {\n      currentRequestId = request.id;\n      \n      // execute the function\n      var result = method.apply(method, request.params);\n\n      if (isPromise(result)) {\n        // promise returned, resolve this and then return\n        result\n            .then(function (result) {\n              if (result instanceof Transfer) {\n                worker.send({\n                  id: request.id,\n                  result: result.message,\n                  error: null\n                }, result.transfer);\n              } else {\n                worker.send({\n                  id: request.id,\n                  result: result,\n                  error: null\n                });\n              }\n              currentRequestId = null;\n            })\n            .catch(function (err) {\n              worker.send({\n                id: request.id,\n                result: null,\n                error: convertError(err),\n              });\n              currentRequestId = null;\n            });\n      }\n      else {\n        // immediate result\n        if (result instanceof Transfer) {\n          worker.send({\n            id: request.id,\n            result: result.message,\n            error: null\n          }, result.transfer);\n        } else {\n          worker.send({\n            id: request.id,\n            result: result,\n            error: null\n          });\n        }\n\n        currentRequestId = null;\n      }\n    }\n    else {\n      throw new Error('Unknown method \"' + request.method + '\"');\n    }\n  }\n  catch (err) {\n    worker.send({\n      id: request.id,\n      result: null,\n      error: convertError(err)\n    });\n  }\n});\n\n/**\n * Register methods to the worker\n * @param {Object} [methods]\n * @param {import('./types.js').WorkerRegisterOptions} [options]\n */\nworker.register = function (methods, options) {\n\n  if (methods) {\n    for (var name in methods) {\n      if (methods.hasOwnProperty(name)) {\n        worker.methods[name] = methods[name];\n        worker.methods[name].worker = publicWorker;\n      }\n    }\n  }\n\n  if (options) {\n    worker.terminationHandler = options.onTerminate;\n    // register listener timeout or default to 1 second\n    worker.abortListenerTimeout = options.abortListenerTimeout || TIMEOUT_DEFAULT;\n  }\n\n  worker.send('ready');\n};\n\nworker.emit = function (payload) {\n  if (currentRequestId) {\n    if (payload instanceof Transfer) {\n      worker.send({\n        id: currentRequestId,\n        isEvent: true,\n        payload: payload.message\n      }, payload.transfer);\n      return;\n    }\n\n    worker.send({\n      id: currentRequestId,\n      isEvent: true,\n      payload\n    });\n  }\n};\n\n\nif (typeof exports !== 'undefined') {\n  exports.add = worker.register;\n  exports.emit = worker.emit;\n}\n"], "names": ["Transfer", "message", "transfer", "Promise", "handler", "parent", "me", "SyntaxError", "_onSuccess", "_onFail", "resolved", "rejected", "pending", "Symbol", "toStringTag", "_process", "onSuccess", "onFail", "push", "then", "resolve", "reject", "s", "_then", "f", "_resolve", "result", "for<PERSON>ach", "fn", "_reject", "error", "cancel", "CancellationError", "timeout", "delay", "timer", "setTimeout", "TimeoutError", "always", "clearTimeout", "callback", "res", "prototype", "finally", "final", "all", "promises", "remaining", "length", "results", "p", "i", "defer", "resolver", "promise", "stack", "Error", "constructor", "name", "_Promise", "require$$0", "require$$1", "TERMINATE_METHOD_ID", "CLEANUP_METHOD_ID", "TIMEOUT_DEFAULT", "worker", "exit", "publicWorker", "addAbortListener", "listener", "abortListeners", "emit", "self", "postMessage", "addEventListener", "on", "event", "data", "send", "process", "WorkerThreads", "require", "_typeof", "code", "parentPort", "bind", "convertError", "Object", "getOwnPropertyNames", "reduce", "product", "defineProperty", "value", "enumerable", "isPromise", "catch", "methods", "run", "args", "Function", "apply", "keys", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "abortListenerTimeout", "terminateAndExit", "_exit", "cleanup", "requestId", "id", "method", "_abort", "map", "timerId", "timeoutPromise", "settlePromise", "err", "currentRequestId", "request", "params", "register", "options", "hasOwnProperty", "onTerminate", "payload", "isEvent", "exports", "add"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAMA,SAASA,QAAQA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IACnC,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;EAEA,IAAAA,QAAc,GAAGF,QAAQ;;;;ECTzB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASG,SAAOA,CAACC,OAAO,EAAEC,MAAM,EAAE;IAChC,IAAIC,EAAE,GAAG,IAAI;EAEb,EAAA,IAAI,EAAE,IAAI,YAAYH,SAAO,CAAC,EAAE;EAC9B,IAAA,MAAM,IAAII,WAAW,CAAC,kDAAkD,CAAC;EAC7E;EAEE,EAAA,IAAI,OAAOH,OAAO,KAAK,UAAU,EAAE;EACjC,IAAA,MAAM,IAAIG,WAAW,CAAC,qDAAqD,CAAC;EAChF;IAEE,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAIC,OAAO,GAAG,EAAE;;EAElB;EACA;EACA;EACA;IACE,IAAI,CAACC,QAAQ,GAAG,KAAK;EACvB;EACA;EACA;IACE,IAAI,CAACC,QAAQ,GAAG,KAAK;EACvB;EACA;EACA;IACE,IAAI,CAACC,OAAO,GAAG,IAAI;EACrB;EACA;EACA;EACE,EAAA,IAAI,CAACC,MAAM,CAACC,WAAW,CAAC,GAAG,SAAS;;EAEtC;EACA;EACA;EACA;EACA;EACA;EACA;IACE,IAAIC,QAAQ,GAAG,SAAXA,QAAQA,CAAaC,SAAS,EAAEC,MAAM,EAAE;EAC1CT,IAAAA,UAAU,CAACU,IAAI,CAACF,SAAS,CAAC;EAC1BP,IAAAA,OAAO,CAACS,IAAI,CAACD,MAAM,CAAC;KACrB;;EAEH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACE,EAAA,IAAI,CAACE,IAAI,GAAG,UAAUH,SAAS,EAAEC,MAAM,EAAE;EACvC,IAAA,OAAO,IAAId,SAAO,CAAC,UAAUiB,OAAO,EAAEC,MAAM,EAAE;EAC5C,MAAA,IAAIC,CAAC,GAAGN,SAAS,GAAGO,KAAK,CAACP,SAAS,EAAEI,OAAO,EAAEC,MAAM,CAAC,GAAGD,OAAO;EAC/D,MAAA,IAAII,CAAC,GAAGP,MAAM,GAAMM,KAAK,CAACN,MAAM,EAAKG,OAAO,EAAEC,MAAM,CAAC,GAAGA,MAAM;EAE9DN,MAAAA,QAAQ,CAACO,CAAC,EAAEE,CAAC,CAAC;OACf,EAAElB,EAAE,CAAC;KACP;;EAEH;EACA;EACA;EACA;EACA;EACE,EAAA,IAAImB,SAAQ,GAAG,SAAXA,QAAQA,CAAaC,MAAM,EAAE;EACnC;MACIpB,EAAE,CAACI,QAAQ,GAAG,IAAI;MAClBJ,EAAE,CAACK,QAAQ,GAAG,KAAK;MACnBL,EAAE,CAACM,OAAO,GAAG,KAAK;EAElBJ,IAAAA,UAAU,CAACmB,OAAO,CAAC,UAAUC,EAAE,EAAE;QAC/BA,EAAE,CAACF,MAAM,CAAC;EAChB,KAAK,CAAC;EAEFX,IAAAA,QAAQ,GAAG,SAAXA,QAAQA,CAAaC,SAAS,EAAEC,MAAM,EAAE;QACtCD,SAAS,CAACU,MAAM,CAAC;OAClB;MAEDD,SAAQ,GAAGI,QAAO,GAAG,SAAVA,OAAOA,GAAe,EAAG;EAEpC,IAAA,OAAOvB,EAAE;KACV;;EAEH;EACA;EACA;EACA;EACA;EACE,EAAA,IAAIuB,QAAO,GAAG,SAAVA,OAAOA,CAAaC,KAAK,EAAE;EACjC;MACIxB,EAAE,CAACI,QAAQ,GAAG,KAAK;MACnBJ,EAAE,CAACK,QAAQ,GAAG,IAAI;MAClBL,EAAE,CAACM,OAAO,GAAG,KAAK;EAElBH,IAAAA,OAAO,CAACkB,OAAO,CAAC,UAAUC,EAAE,EAAE;QAC5BA,EAAE,CAACE,KAAK,CAAC;EACf,KAAK,CAAC;EAEFf,IAAAA,QAAQ,GAAG,SAAXA,QAAQA,CAAaC,SAAS,EAAEC,MAAM,EAAE;QACtCA,MAAM,CAACa,KAAK,CAAC;OACd;MAEDL,SAAQ,GAAGI,QAAO,GAAG,SAAVA,OAAOA,GAAe,EAAG;EAEpC,IAAA,OAAOvB,EAAE;KACV;;EAEH;EACA;EACA;EACA;IACE,IAAI,CAACyB,MAAM,GAAG,YAAY;EACxB,IAAA,IAAI1B,MAAM,EAAE;QACVA,MAAM,CAAC0B,MAAM,EAAE;EACrB,KAAK,MACI;EACHF,MAAAA,QAAO,CAAC,IAAIG,iBAAiB,EAAE,CAAC;EACtC;EAEI,IAAA,OAAO1B,EAAE;KACV;;EAEH;EACA;EACA;EACA;EACA;EACA;EACA;EACE,EAAA,IAAI,CAAC2B,OAAO,GAAG,UAAUC,KAAK,EAAE;EAC9B,IAAA,IAAI7B,MAAM,EAAE;EACVA,MAAAA,MAAM,CAAC4B,OAAO,CAACC,KAAK,CAAC;EAC3B,KAAK,MACI;EACH,MAAA,IAAIC,KAAK,GAAGC,UAAU,CAAC,YAAY;UACjCP,QAAO,CAAC,IAAIQ,YAAY,CAAC,0BAA0B,GAAGH,KAAK,GAAG,KAAK,CAAC,CAAC;SACtE,EAAEA,KAAK,CAAC;QAET5B,EAAE,CAACgC,MAAM,CAAC,YAAY;UACpBC,YAAY,CAACJ,KAAK,CAAC;EAC3B,OAAO,CAAC;EACR;EAEI,IAAA,OAAO7B,EAAE;KACV;;EAEH;IACEF,OAAO,CAAC,UAAUsB,MAAM,EAAE;MACxBD,SAAQ,CAACC,MAAM,CAAC;KACjB,EAAE,UAAUI,KAAK,EAAE;MAClBD,QAAO,CAACC,KAAK,CAAC;EAClB,GAAG,CAAC;EACJ;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASP,KAAKA,CAACiB,QAAQ,EAAEpB,OAAO,EAAEC,MAAM,EAAE;IACxC,OAAO,UAAUK,MAAM,EAAE;MACvB,IAAI;EACF,MAAA,IAAIe,GAAG,GAAGD,QAAQ,CAACd,MAAM,CAAC;EAC1B,MAAA,IAAIe,GAAG,IAAI,OAAOA,GAAG,CAACtB,IAAI,KAAK,UAAU,IAAI,OAAOsB,GAAG,CAAC,OAAO,CAAC,KAAK,UAAU,EAAE;EACvF;EACQA,QAAAA,GAAG,CAACtB,IAAI,CAACC,OAAO,EAAEC,MAAM,CAAC;EACjC,OAAO,MACI;UACHD,OAAO,CAACqB,GAAG,CAAC;EACpB;OACK,CACD,OAAOX,KAAK,EAAE;QACZT,MAAM,CAACS,KAAK,CAAC;EACnB;KACG;EACH;;EAEA;EACA;EACA;EACA;EACA;EACA;AACA3B,WAAO,CAACuC,SAAS,CAAC,OAAO,CAAC,GAAG,UAAUzB,MAAM,EAAE;EAC7C,EAAA,OAAO,IAAI,CAACE,IAAI,CAAC,IAAI,EAAEF,MAAM,CAAC;EAChC,CAAC;;EAED;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;AACAd,WAAO,CAACuC,SAAS,CAACJ,MAAM,GAAG,UAAUV,EAAE,EAAE;EACvC,EAAA,OAAO,IAAI,CAACT,IAAI,CAACS,EAAE,EAAEA,EAAE,CAAC;EAC1B,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;AACAzB,WAAO,CAACuC,SAAS,CAACC,OAAO,GAAG,UAAUf,EAAE,EAAE;IACxC,IAAMtB,EAAE,GAAG,IAAI;EAEf,EAAA,IAAMsC,KAAK,GAAG,SAARA,KAAKA,GAAc;EACvB,IAAA,OAAO,IAAIzC,SAAO,CAAC,UAACiB,OAAO,EAAA;QAAA,OAAKA,OAAO,EAAE;EAAA,KAAA,CAAC,CACvCD,IAAI,CAACS,EAAE,CAAC,CACRT,IAAI,CAAC,YAAA;EAAA,MAAA,OAAMb,EAAE;OAAA,CAAC;KAClB;EAED,EAAA,OAAO,IAAI,CAACa,IAAI,CAACyB,KAAK,EAAEA,KAAK,CAAC;EAChC,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;AACAzC,WAAO,CAAC0C,GAAG,GAAG,UAAUC,QAAQ,EAAC;EAC/B,EAAA,OAAO,IAAI3C,SAAO,CAAC,UAAUiB,OAAO,EAAEC,MAAM,EAAE;EAC5C,IAAA,IAAI0B,SAAS,GAAGD,QAAQ,CAACE,MAAM;EAC3BC,MAAAA,OAAO,GAAG,EAAE;EAEhB,IAAA,IAAIF,SAAS,EAAE;EACbD,MAAAA,QAAQ,CAACnB,OAAO,CAAC,UAAUuB,CAAC,EAAEC,CAAC,EAAE;EAC/BD,QAAAA,CAAC,CAAC/B,IAAI,CAAC,UAAUO,MAAM,EAAE;EACvBuB,UAAAA,OAAO,CAACE,CAAC,CAAC,GAAGzB,MAAM;EACnBqB,UAAAA,SAAS,EAAE;YACX,IAAIA,SAAS,IAAI,CAAC,EAAE;cAClB3B,OAAO,CAAC6B,OAAO,CAAC;EAC5B;WACS,EAAE,UAAUnB,KAAK,EAAE;EAClBiB,UAAAA,SAAS,GAAG,CAAC;YACb1B,MAAM,CAACS,KAAK,CAAC;EACvB,SAAS,CAAC;EACV,OAAO,CAAC;EACR,KAAK,MACI;QACHV,OAAO,CAAC6B,OAAO,CAAC;EACtB;EACA,GAAG,CAAC;EACJ,CAAC;;EAED;EACA;EACA;EACA;AACA9C,WAAO,CAACiD,KAAK,GAAG,YAAY;IAC1B,IAAIC,QAAQ,GAAG,EAAE;IAEjBA,QAAQ,CAACC,OAAO,GAAG,IAAInD,SAAO,CAAC,UAAUiB,OAAO,EAAEC,MAAM,EAAE;MACxDgC,QAAQ,CAACjC,OAAO,GAAGA,OAAO;MAC1BiC,QAAQ,CAAChC,MAAM,GAAGA,MAAM;EAC5B,GAAG,CAAC;EAEF,EAAA,OAAOgC,QAAQ;EACjB,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA,SAASrB,iBAAiBA,CAAC/B,OAAO,EAAE;EAClC,EAAA,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAI,mBAAmB;IAC7C,IAAI,CAACsD,KAAK,GAAI,IAAIC,KAAK,EAAE,CAAED,KAAK;EAClC;EAEAvB,iBAAiB,CAACU,SAAS,GAAG,IAAIc,KAAK,EAAE;EACzCxB,iBAAiB,CAACU,SAAS,CAACe,WAAW,GAAGD,KAAK;EAC/CxB,iBAAiB,CAACU,SAAS,CAACgB,IAAI,GAAG,mBAAmB;AAEtDvD,WAAO,CAAC6B,iBAAiB,GAAGA,iBAAiB;;EAG7C;EACA;EACA;EACA;EACA;EACA,SAASK,YAAYA,CAACpC,OAAO,EAAE;EAC7B,EAAA,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAI,kBAAkB;IAC5C,IAAI,CAACsD,KAAK,GAAI,IAAIC,KAAK,EAAE,CAAED,KAAK;EAClC;EAEAlB,YAAY,CAACK,SAAS,GAAG,IAAIc,KAAK,EAAE;EACpCnB,YAAY,CAACK,SAAS,CAACe,WAAW,GAAGD,KAAK;EAC1CnB,YAAY,CAACK,SAAS,CAACgB,IAAI,GAAG,cAAc;AAE5CvD,WAAO,CAACkC,YAAY,GAAGA,YAAY;EAGnCsB,QAAA,CAAAxD,OAAe,GAAGA;;;ICrTlB,IAAIH,QAAQ,GAAG4D,QAAqB;;EAEpC;EACA;EACA;EACA,EAAA,IAAIzD,OAAO,GAAG0D,QAAoB,CAAC1D,OAAO;EAC1C;EACA;EACA;EACA;IACA,IAAI2D,mBAAmB,GAAG,0BAA0B;;EAEpD;EACA;EACA;EACA;IACA,IAAIC,iBAAiB,GAAG,wBAAwB;EAChD;;IAGA,IAAIC,eAAe,GAAG,IAAK;;EAE3B;EACA;EACA,EAAA,IAAIC,MAAM,GAAG;EACXC,IAAAA,IAAI,EAAE,SAANA,IAAIA,GAAa;KAClB;;EAED;EACA;EACA,EAAA,IAAIC,YAAY,GAAG;EACnB;EACA;EACA;EACA;EACA;EACEC,IAAAA,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAWC,QAAQ,EAAE;EACnCJ,MAAAA,MAAM,CAACK,cAAc,CAACpD,IAAI,CAACmD,QAAQ,CAAC;OACrC;EAEH;EACA;EACA;EACA;MACEE,IAAI,EAAEN,MAAM,CAACM;KACd;EAED,EAAA,IAAI,OAAOC,IAAI,KAAK,WAAW,IAAI,OAAOC,WAAW,KAAK,UAAU,IAAI,OAAOC,gBAAgB,KAAK,UAAU,EAAE;EAChH;EACET,IAAAA,MAAM,CAACU,EAAE,GAAG,UAAUC,KAAK,EAAEpC,QAAQ,EAAE;EACrCkC,MAAAA,gBAAgB,CAACE,KAAK,EAAE,UAAU3E,OAAO,EAAE;EACzCuC,QAAAA,QAAQ,CAACvC,OAAO,CAAC4E,IAAI,CAAC;EAC5B,OAAK,CAAC;OACH;EACDZ,IAAAA,MAAM,CAACa,IAAI,GAAG,UAAU7E,OAAO,EAAEC,QAAQ,EAAE;QACxCA,QAAQ,GAAGuE,WAAW,CAACxE,OAAO,EAAEC,QAAQ,CAAC,GAAGuE,WAAW,CAAExE,OAAO,CAAC;OACnE;EACH,GAAC,MACI,IAAI,OAAO8E,OAAO,KAAK,WAAW,EAAE;EACzC;;EAEE,IAAA,IAAIC,aAAa;MACjB,IAAI;EACFA,MAAAA,aAAa,GAAGC,OAAA,CAAQ,gBAAgB,CAAC;OAC1C,CAAC,OAAMnD,KAAK,EAAE;EACb,MAAA,IAAIoD,OAAA,CAAOpD,KAAK,CAAA,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,CAACqD,IAAI,KAAK,kBAAkB,EAAE,CAErF,MAAM;EACL,QAAA,MAAMrD,KAAK;EACjB;EACA;EAEE,IAAA,IAAIkD,aAAa;EAEfA,IAAAA,aAAa,CAACI,UAAU,KAAK,IAAI,EAAE;EACnC,MAAA,IAAIA,UAAU,GAAIJ,aAAa,CAACI,UAAU;QAC1CnB,MAAM,CAACa,IAAI,GAAGM,UAAU,CAACX,WAAW,CAACY,IAAI,CAACD,UAAU,CAAC;QACrDnB,MAAM,CAACU,EAAE,GAAGS,UAAU,CAACT,EAAE,CAACU,IAAI,CAACD,UAAU,CAAC;QAC1CnB,MAAM,CAACC,IAAI,GAAGa,OAAO,CAACb,IAAI,CAACmB,IAAI,CAACN,OAAO,CAAC;EAC5C,KAAG,MAAM;QACLd,MAAM,CAACU,EAAE,GAAGI,OAAO,CAACJ,EAAE,CAACU,IAAI,CAACN,OAAO,CAAC;EACxC;EACId,MAAAA,MAAM,CAACa,IAAI,GAAG,UAAU7E,OAAO,EAAE;EAC/B8E,QAAAA,OAAO,CAACD,IAAI,CAAC7E,OAAO,CAAC;SACtB;EACL;EACIgE,MAAAA,MAAM,CAACU,EAAE,CAAC,YAAY,EAAE,YAAY;EAClCI,QAAAA,OAAO,CAACb,IAAI,CAAC,CAAC,CAAC;EACrB,OAAK,CAAC;QACFD,MAAM,CAACC,IAAI,GAAGa,OAAO,CAACb,IAAI,CAACmB,IAAI,CAACN,OAAO,CAAC;EAC5C;EACA,GAAC,MACI;EACH,IAAA,MAAM,IAAIvB,KAAK,CAAC,qCAAqC,CAAC;EACxD;IAEA,SAAS8B,YAAYA,CAACxD,KAAK,EAAE;EAC3B,IAAA,OAAOyD,MAAM,CAACC,mBAAmB,CAAC1D,KAAK,CAAC,CAAC2D,MAAM,CAAC,UAASC,OAAO,EAAEhC,IAAI,EAAE;EACtE,MAAA,OAAO6B,MAAM,CAACI,cAAc,CAACD,OAAO,EAAEhC,IAAI,EAAE;EAC/CkC,QAAAA,KAAK,EAAE9D,KAAK,CAAC4B,IAAI,CAAC;EAClBmC,QAAAA,UAAU,EAAE;EACb,OAAK,CAAC;OACH,EAAE,EAAE,CAAC;EACR;;EAEA;EACA;EACA;EACA;EACA;EACA;IACA,SAASC,SAASA,CAACF,KAAK,EAAE;EACxB,IAAA,OAAOA,KAAK,IAAK,OAAOA,KAAK,CAACzE,IAAI,KAAK,UAAW,IAAK,OAAOyE,KAAK,CAACG,KAAK,KAAK,UAAW;EAC3F;;EAEA;EACA9B,EAAAA,MAAM,CAAC+B,OAAO,GAAG,EAAE;;EAEnB;EACA;EACA;EACA;EACA;EACA;IACA/B,MAAM,CAAC+B,OAAO,CAACC,GAAG,GAAG,SAASA,GAAGA,CAACrE,EAAE,EAAEsE,IAAI,EAAE;MAC1C,IAAI1E,CAAC,GAAG,IAAI2E,QAAQ,CAAC,UAAU,GAAGvE,EAAE,GAAG,2BAA2B,CAAC;MACnEJ,CAAC,CAACyC,MAAM,GAAGE,YAAY;EACvB,IAAA,OAAO3C,CAAC,CAAC4E,KAAK,CAAC5E,CAAC,EAAE0E,IAAI,CAAC;KACxB;;EAED;EACA;EACA;EACA;IACAjC,MAAM,CAAC+B,OAAO,CAACA,OAAO,GAAG,SAASA,OAAOA,GAAG;EAC1C,IAAA,OAAOT,MAAM,CAACc,IAAI,CAACpC,MAAM,CAAC+B,OAAO,CAAC;KACnC;;EAED;EACA;EACA;IACA/B,MAAM,CAACqC,kBAAkB,GAAGC,SAAS;IAErCtC,MAAM,CAACuC,oBAAoB,GAAGxC,eAAe;;EAE7C;EACA;EACA;EACA;IACAC,MAAM,CAACK,cAAc,GAAG,EAAE;;EAE1B;EACA;EACA;EACA;EACA;EACAL,EAAAA,MAAM,CAACwC,gBAAgB,GAAG,UAAStB,IAAI,EAAE;EACvC,IAAA,IAAIuB,KAAK,GAAG,SAARA,KAAKA,GAAc;EACrBzC,MAAAA,MAAM,CAACC,IAAI,CAACiB,IAAI,CAAC;OAClB;EAED,IAAA,IAAG,CAAClB,MAAM,CAACqC,kBAAkB,EAAE;QAC7B,OAAOI,KAAK,EAAE;EAClB;EAEE,IAAA,IAAIhF,MAAM,GAAGuC,MAAM,CAACqC,kBAAkB,CAACnB,IAAI,CAAC;EAC5C,IAAA,IAAIW,SAAS,CAACpE,MAAM,CAAC,EAAE;EACrBA,MAAAA,MAAM,CAACP,IAAI,CAACuF,KAAK,EAAEA,KAAK,CAAC;EAEzB,MAAA,OAAOhF,MAAM;EACjB,KAAG,MAAM;EACLgF,MAAAA,KAAK,EAAE;EACP,MAAA,OAAO,IAAIvG,OAAO,CAAC,UAAUsB,QAAQ,EAAEJ,MAAM,EAAE;EAC7CA,QAAAA,MAAM,CAAC,IAAImC,KAAK,CAAC,oBAAoB,CAAC,CAAC;EAC7C,OAAK,CAAC;EACN;KACC;;EAID;EACA;EACA;EACA;EACA;EACAS,EAAAA,MAAM,CAAC0C,OAAO,GAAG,UAASC,SAAS,EAAE;EAEnC,IAAA,IAAI,CAAC3C,MAAM,CAACK,cAAc,CAACtB,MAAM,EAAE;QACjCiB,MAAM,CAACa,IAAI,CAAC;EACV+B,QAAAA,EAAE,EAAED,SAAS;EACbE,QAAAA,MAAM,EAAE/C,iBAAiB;EACzBjC,QAAAA,KAAK,EAAEwD,YAAY,CAAC,IAAI9B,KAAK,CAAC,oBAAoB,CAAC;EACzD,OAAK,CAAC;;EAEN;EACA;EACI,MAAA,OAAO,IAAIrD,OAAO,CAAC,UAASiB,OAAO,EAAE;EAAEA,QAAAA,OAAO,EAAE;EAAC,OAAE,CAAC;EACxD;EAGE,IAAA,IAAIsF,KAAK,GAAG,SAARA,KAAKA,GAAc;QACrBzC,MAAM,CAACC,IAAI,EAAE;OACd;EAED,IAAA,IAAI6C,MAAM,GAAG,SAATA,MAAMA,GAAc;EACtB,MAAA,IAAI,CAAC9C,MAAM,CAACK,cAAc,CAACtB,MAAM,EAAE;UACjCiB,MAAM,CAACK,cAAc,GAAG,EAAE;EAChC;OACG;MAED,IAAMxB,QAAQ,GAAGmB,MAAM,CAACK,cAAc,CAAC0C,GAAG,CAAC,UAAA3C,QAAQ,EAAA;QAAA,OAAIA,QAAQ,EAAE;OAAA,CAAC;EAClE,IAAA,IAAI4C,OAAO;MACX,IAAMC,cAAc,GAAG,IAAI/G,OAAO,CAAC,UAACsB,QAAQ,EAAEJ,MAAM,EAAK;QACvD4F,OAAO,GAAG7E,UAAU,CAAC,YAAY;EAC/Bf,QAAAA,MAAM,CAAC,IAAImC,KAAK,CAAC,2DAA2D,CAAC,CAAC;EACpF,OAAK,EAAES,MAAM,CAACuC,oBAAoB,CAAC;EACnC,KAAG,CAAC;;EAEJ;MACE,IAAMW,aAAa,GAAGhH,OAAO,CAAC0C,GAAG,CAACC,QAAQ,CAAC,CAAC3B,IAAI,CAAC,YAAW;QAC1DoB,YAAY,CAAC0E,OAAO,CAAC;EACrBF,MAAAA,MAAM,EAAE;EACZ,KAAG,EAAE,YAAW;QACZxE,YAAY,CAAC0E,OAAO,CAAC;EACrBP,MAAAA,KAAK,EAAE;EACX,KAAG,CAAC;;EAEJ;EACA;EACA;EACA;EACA;EACA;EACA;EACE,IAAA,OAAO,IAAIvG,OAAO,CAAC,UAASiB,OAAO,EAAEC,MAAM,EAAE;EAC3C8F,MAAAA,aAAa,CAAChG,IAAI,CAACC,OAAO,EAAEC,MAAM,CAAC;EACnC6F,MAAAA,cAAc,CAAC/F,IAAI,CAACC,OAAO,EAAEC,MAAM,CAAC;EACxC,KAAG,CAAC,CAACF,IAAI,CAAC,YAAW;QACjB8C,MAAM,CAACa,IAAI,CAAC;EACV+B,QAAAA,EAAE,EAAED,SAAS;EACbE,QAAAA,MAAM,EAAE/C,iBAAiB;EACzBjC,QAAAA,KAAK,EAAE;EACb,OAAK,CAAC;OACH,EAAE,UAASsF,GAAG,EAAE;QACfnD,MAAM,CAACa,IAAI,CAAC;EACV+B,QAAAA,EAAE,EAAED,SAAS;EACbE,QAAAA,MAAM,EAAE/C,iBAAiB;EACzBjC,QAAAA,KAAK,EAAEsF,GAAG,GAAG9B,YAAY,CAAC8B,GAAG,CAAC,GAAG;EACvC,OAAK,CAAC;EACN,KAAG,CAAC;KACH;IAED,IAAIC,gBAAgB,GAAG,IAAI;EAE3BpD,EAAAA,MAAM,CAACU,EAAE,CAAC,SAAS,EAAE,UAAU2C,OAAO,EAAE;MACtC,IAAIA,OAAO,KAAKxD,mBAAmB,EAAE;EACnC,MAAA,OAAOG,MAAM,CAACwC,gBAAgB,CAAC,CAAC,CAAC;EACrC;EAEE,IAAA,IAAIa,OAAO,CAACR,MAAM,KAAK/C,iBAAiB,EAAE;EACxC,MAAA,OAAOE,MAAM,CAAC0C,OAAO,CAACW,OAAO,CAACT,EAAE,CAAC;EACrC;MAEE,IAAI;QACF,IAAIC,MAAM,GAAG7C,MAAM,CAAC+B,OAAO,CAACsB,OAAO,CAACR,MAAM,CAAC;EAE3C,MAAA,IAAIA,MAAM,EAAE;UACVO,gBAAgB,GAAGC,OAAO,CAACT,EAAE;;EAEnC;UACM,IAAInF,MAAM,GAAGoF,MAAM,CAACV,KAAK,CAACU,MAAM,EAAEQ,OAAO,CAACC,MAAM,CAAC;EAEjD,QAAA,IAAIzB,SAAS,CAACpE,MAAM,CAAC,EAAE;EAC7B;EACQA,UAAAA,MAAM,CACDP,IAAI,CAAC,UAAUO,MAAM,EAAE;cACtB,IAAIA,MAAM,YAAY1B,QAAQ,EAAE;gBAC9BiE,MAAM,CAACa,IAAI,CAAC;kBACV+B,EAAE,EAAES,OAAO,CAACT,EAAE;kBACdnF,MAAM,EAAEA,MAAM,CAACzB,OAAO;EACtB6B,gBAAAA,KAAK,EAAE;EACzB,eAAiB,EAAEJ,MAAM,CAACxB,QAAQ,CAAC;EACnC,aAAe,MAAM;gBACL+D,MAAM,CAACa,IAAI,CAAC;kBACV+B,EAAE,EAAES,OAAO,CAACT,EAAE;EACdnF,gBAAAA,MAAM,EAAEA,MAAM;EACdI,gBAAAA,KAAK,EAAE;EACzB,eAAiB,CAAC;EAClB;EACcuF,YAAAA,gBAAgB,GAAG,IAAI;EACrC,WAAa,CAAC,CACDtB,KAAK,CAAC,UAAUqB,GAAG,EAAE;cACpBnD,MAAM,CAACa,IAAI,CAAC;gBACV+B,EAAE,EAAES,OAAO,CAACT,EAAE;EACdnF,cAAAA,MAAM,EAAE,IAAI;gBACZI,KAAK,EAAEwD,YAAY,CAAC8B,GAAG;EACvC,aAAe,CAAC;EACFC,YAAAA,gBAAgB,GAAG,IAAI;EACrC,WAAa,CAAC;EACd,SAAO,MACI;EACX;YACQ,IAAI3F,MAAM,YAAY1B,QAAQ,EAAE;cAC9BiE,MAAM,CAACa,IAAI,CAAC;gBACV+B,EAAE,EAAES,OAAO,CAACT,EAAE;gBACdnF,MAAM,EAAEA,MAAM,CAACzB,OAAO;EACtB6B,cAAAA,KAAK,EAAE;EACnB,aAAW,EAAEJ,MAAM,CAACxB,QAAQ,CAAC;EAC7B,WAAS,MAAM;cACL+D,MAAM,CAACa,IAAI,CAAC;gBACV+B,EAAE,EAAES,OAAO,CAACT,EAAE;EACdnF,cAAAA,MAAM,EAAEA,MAAM;EACdI,cAAAA,KAAK,EAAE;EACnB,aAAW,CAAC;EACZ;EAEQuF,UAAAA,gBAAgB,GAAG,IAAI;EAC/B;EACA,OAAK,MACI;UACH,MAAM,IAAI7D,KAAK,CAAC,kBAAkB,GAAG8D,OAAO,CAACR,MAAM,GAAG,GAAG,CAAC;EAChE;OACG,CACD,OAAOM,GAAG,EAAE;QACVnD,MAAM,CAACa,IAAI,CAAC;UACV+B,EAAE,EAAES,OAAO,CAACT,EAAE;EACdnF,QAAAA,MAAM,EAAE,IAAI;UACZI,KAAK,EAAEwD,YAAY,CAAC8B,GAAG;EAC7B,OAAK,CAAC;EACN;EACA,GAAC,CAAC;;EAEF;EACA;EACA;EACA;EACA;EACAnD,EAAAA,MAAM,CAACuD,QAAQ,GAAG,UAAUxB,OAAO,EAAEyB,OAAO,EAAE;EAE5C,IAAA,IAAIzB,OAAO,EAAE;EACX,MAAA,KAAK,IAAItC,IAAI,IAAIsC,OAAO,EAAE;EACxB,QAAA,IAAIA,OAAO,CAAC0B,cAAc,CAAChE,IAAI,CAAC,EAAE;YAChCO,MAAM,CAAC+B,OAAO,CAACtC,IAAI,CAAC,GAAGsC,OAAO,CAACtC,IAAI,CAAC;YACpCO,MAAM,CAAC+B,OAAO,CAACtC,IAAI,CAAC,CAACO,MAAM,GAAGE,YAAY;EAClD;EACA;EACA;EAEE,IAAA,IAAIsD,OAAO,EAAE;EACXxD,MAAAA,MAAM,CAACqC,kBAAkB,GAAGmB,OAAO,CAACE,WAAW;EACnD;EACI1D,MAAAA,MAAM,CAACuC,oBAAoB,GAAGiB,OAAO,CAACjB,oBAAoB,IAAIxC,eAAe;EACjF;EAEEC,IAAAA,MAAM,CAACa,IAAI,CAAC,OAAO,CAAC;KACrB;EAEDb,EAAAA,MAAM,CAACM,IAAI,GAAG,UAAUqD,OAAO,EAAE;EAC/B,IAAA,IAAIP,gBAAgB,EAAE;QACpB,IAAIO,OAAO,YAAY5H,QAAQ,EAAE;UAC/BiE,MAAM,CAACa,IAAI,CAAC;EACV+B,UAAAA,EAAE,EAAEQ,gBAAgB;EACpBQ,UAAAA,OAAO,EAAE,IAAI;YACbD,OAAO,EAAEA,OAAO,CAAC3H;EACzB,SAAO,EAAE2H,OAAO,CAAC1H,QAAQ,CAAC;EACpB,QAAA;EACN;QAEI+D,MAAM,CAACa,IAAI,CAAC;EACV+B,QAAAA,EAAE,EAAEQ,gBAAgB;EACpBQ,QAAAA,OAAO,EAAE,IAAI;EACbD,QAAAA,OAAO,EAAPA;EACN,OAAK,CAAC;EACN;KACC;IAGmC;EAClCE,IAAAA,OAAA,CAAAC,GAAA,GAAc9D,MAAM,CAACuD,QAAQ;EAC7BM,IAAAA,OAAA,CAAAvD,IAAA,GAAeN,MAAM,CAACM,IAAI;EAC5B;;;;;;;;;;"}