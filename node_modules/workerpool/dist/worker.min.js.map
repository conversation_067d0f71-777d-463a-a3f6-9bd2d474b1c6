{"version": 3, "file": "worker.min.js", "sources": ["../src/transfer.js", "../src/Promise.js", "../src/worker.js"], "sourcesContent": ["/**\n * The helper class for transferring data from the worker to the main thread.\n *\n * @param {Object} message The object to deliver to the main thread.\n * @param {Object[]} transfer An array of transferable Objects to transfer ownership of.\n */\nfunction Transfer(message, transfer) {\n  this.message = message;\n  this.transfer = transfer;\n}\n\nmodule.exports = Transfer;\n", "'use strict';\n\n/**\n * Promise\n *\n * Inspired by https://gist.github.com/RubaXa/8501359 from RubaXa <<EMAIL>>\n * @template T\n * @template [E=Error]\n * @param {Function} handler   Called as handler(resolve: Function, reject: Function)\n * @param {Promise} [parent]   Parent promise for propagation of cancel and timeout\n */\nfunction Promise(handler, parent) {\n  var me = this;\n\n  if (!(this instanceof Promise)) {\n    throw new SyntaxError('Constructor must be called with the new operator');\n  }\n\n  if (typeof handler !== 'function') {\n    throw new SyntaxError('Function parameter handler(resolve, reject) missing');\n  }\n\n  var _onSuccess = [];\n  var _onFail = [];\n\n  // status\n  /**\n   * @readonly\n   */\n  this.resolved = false;\n  /**\n   * @readonly\n   */\n  this.rejected = false;\n  /**\n   * @readonly\n   */\n  this.pending = true;\n  /**\n   * @readonly\n   */\n  this[Symbol.toStringTag] = 'Promise';\n\n  /**\n   * Process onSuccess and onFail callbacks: add them to the queue.\n   * Once the promise is resolved, the function _promise is replace.\n   * @param {Function} onSuccess\n   * @param {Function} onFail\n   * @private\n   */\n  var _process = function (onSuccess, onFail) {\n    _onSuccess.push(onSuccess);\n    _onFail.push(onFail);\n  };\n\n  /**\n   * Add an onSuccess callback and optionally an onFail callback to the Promise\n   * @template TT\n   * @template [TE=never]\n   * @param {(r: T) => TT | PromiseLike<TT>} onSuccess\n   * @param {(r: E) => TE | PromiseLike<TE>} [onFail]\n   * @returns {Promise<TT | TE, any>} promise\n   */\n  this.then = function (onSuccess, onFail) {\n    return new Promise(function (resolve, reject) {\n      var s = onSuccess ? _then(onSuccess, resolve, reject) : resolve;\n      var f = onFail    ? _then(onFail,    resolve, reject) : reject;\n\n      _process(s, f);\n    }, me);\n  };\n\n  /**\n   * Resolve the promise\n   * @param {*} result\n   * @type {Function}\n   */\n  var _resolve = function (result) {\n    // update status\n    me.resolved = true;\n    me.rejected = false;\n    me.pending = false;\n\n    _onSuccess.forEach(function (fn) {\n      fn(result);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onSuccess(result);\n    };\n\n    _resolve = _reject = function () { };\n\n    return me;\n  };\n\n  /**\n   * Reject the promise\n   * @param {Error} error\n   * @type {Function}\n   */\n  var _reject = function (error) {\n    // update status\n    me.resolved = false;\n    me.rejected = true;\n    me.pending = false;\n\n    _onFail.forEach(function (fn) {\n      fn(error);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onFail(error);\n    };\n\n    _resolve = _reject = function () { }\n\n    return me;\n  };\n\n  /**\n   * Cancel the promise. This will reject the promise with a CancellationError\n   * @returns {this} self\n   */\n  this.cancel = function () {\n    if (parent) {\n      parent.cancel();\n    }\n    else {\n      _reject(new CancellationError());\n    }\n\n    return me;\n  };\n\n  /**\n   * Set a timeout for the promise. If the promise is not resolved within\n   * the time, the promise will be cancelled and a TimeoutError is thrown.\n   * If the promise is resolved in time, the timeout is removed.\n   * @param {number} delay     Delay in milliseconds\n   * @returns {this} self\n   */\n  this.timeout = function (delay) {\n    if (parent) {\n      parent.timeout(delay);\n    }\n    else {\n      var timer = setTimeout(function () {\n        _reject(new TimeoutError('Promise timed out after ' + delay + ' ms'));\n      }, delay);\n\n      me.always(function () {\n        clearTimeout(timer);\n      });\n    }\n\n    return me;\n  };\n\n  // attach handler passing the resolve and reject functions\n  handler(function (result) {\n    _resolve(result);\n  }, function (error) {\n    _reject(error);\n  });\n}\n\n/**\n * Execute given callback, then call resolve/reject based on the returned result\n * @param {Function} callback\n * @param {Function} resolve\n * @param {Function} reject\n * @returns {Function}\n * @private\n */\nfunction _then(callback, resolve, reject) {\n  return function (result) {\n    try {\n      var res = callback(result);\n      if (res && typeof res.then === 'function' && typeof res['catch'] === 'function') {\n        // method returned a promise\n        res.then(resolve, reject);\n      }\n      else {\n        resolve(res);\n      }\n    }\n    catch (error) {\n      reject(error);\n    }\n  }\n}\n\n/**\n * Add an onFail callback to the Promise\n * @template TT\n * @param {(error: E) => TT | PromiseLike<TT>} onFail\n * @returns {Promise<T | TT>} promise\n */\nPromise.prototype['catch'] = function (onFail) {\n  return this.then(null, onFail);\n};\n\n// TODO: add support for Promise.catch(Error, callback)\n// TODO: add support for Promise.catch(Error, Error, callback)\n\n/**\n * Execute given callback when the promise either resolves or rejects.\n * @template TT\n * @param {() => Promise<TT>} fn\n * @returns {Promise<TT>} promise\n */\nPromise.prototype.always = function (fn) {\n  return this.then(fn, fn);\n};\n\n/**\n  * Execute given callback when the promise either resolves or rejects.\n  * Same semantics as Node's Promise.finally()\n  * @param {Function | null | undefined} [fn]\n  * @returns {Promise} promise\n  */\nPromise.prototype.finally = function (fn) {\n  const me = this;\n\n  const final = function() {\n    return new Promise((resolve) => resolve())\n      .then(fn)\n      .then(() => me);\n  };\n\n  return this.then(final, final);\n}\n\n/**\n * Create a promise which resolves when all provided promises are resolved,\n * and fails when any of the promises resolves.\n * @param {Promise[]} promises\n * @returns {Promise<any[], any>} promise\n */\nPromise.all = function (promises){\n  return new Promise(function (resolve, reject) {\n    var remaining = promises.length,\n        results = [];\n\n    if (remaining) {\n      promises.forEach(function (p, i) {\n        p.then(function (result) {\n          results[i] = result;\n          remaining--;\n          if (remaining == 0) {\n            resolve(results);\n          }\n        }, function (error) {\n          remaining = 0;\n          reject(error);\n        });\n      });\n    }\n    else {\n      resolve(results);\n    }\n  });\n};\n\n/**\n * Create a promise resolver\n * @returns {{promise: Promise, resolve: Function, reject: Function}} resolver\n */\nPromise.defer = function () {\n  var resolver = {};\n\n  resolver.promise = new Promise(function (resolve, reject) {\n    resolver.resolve = resolve;\n    resolver.reject = reject;\n  });\n\n  return resolver;\n};\n\n/**\n * Create a cancellation error\n * @param {String} [message]\n * @extends Error\n */\nfunction CancellationError(message) {\n  this.message = message || 'promise cancelled';\n  this.stack = (new Error()).stack;\n}\n\nCancellationError.prototype = new Error();\nCancellationError.prototype.constructor = Error;\nCancellationError.prototype.name = 'CancellationError';\n\nPromise.CancellationError = CancellationError;\n\n\n/**\n * Create a timeout error\n * @param {String} [message]\n * @extends Error\n */\nfunction TimeoutError(message) {\n  this.message = message || 'timeout exceeded';\n  this.stack = (new Error()).stack;\n}\n\nTimeoutError.prototype = new Error();\nTimeoutError.prototype.constructor = Error;\nTimeoutError.prototype.name = 'TimeoutError';\n\nPromise.TimeoutError = TimeoutError;\n\n\nexports.Promise = Promise;\n", "/**\n * worker must be started as a child process or a web worker.\n * It listens for RPC messages from the parent process.\n */\n\nvar Transfer = require('./transfer');\n\n/**\n * worker must handle async cleanup handlers. Use custom Promise implementation. \n*/\nvar Promise = require('./Promise').Promise;\n/**\n * Special message sent by parent which causes the worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\n/**\n * Special message by parent which causes a child process worker to perform cleaup\n * steps before determining if the child process worker should be terminated.\n*/\nvar CLEANUP_METHOD_ID = '__workerpool-cleanup__';\n// var nodeOSPlatform = require('./environment').nodeOSPlatform;\n\n\nvar TIMEOUT_DEFAULT = 1_000;\n\n// create a worker API for sending and receiving messages which works both on\n// node.js and in the browser\nvar worker = {\n  exit: function() {}\n};\n\n// api for in worker communication with parent process\n// works in both node.js and the browser\nvar publicWorker = {\n  /**\n   * Registers listeners which will trigger when a task is timed out or cancled. If all listeners resolve, the worker executing the given task will not be terminated.\n   * *Note*: If there is a blocking operation within a listener, the worker will be terminated.\n   * @param {() => Promise<void>} listener\n  */\n  addAbortListener: function(listener) {\n    worker.abortListeners.push(listener);\n  },\n\n  /**\n    * Emit an event from the worker thread to the main thread.\n    * @param {any} payload\n  */\n  emit: worker.emit\n};\n\nif (typeof self !== 'undefined' && typeof postMessage === 'function' && typeof addEventListener === 'function') {\n  // worker in the browser\n  worker.on = function (event, callback) {\n    addEventListener(event, function (message) {\n      callback(message.data);\n    })\n  };\n  worker.send = function (message, transfer) {\n     transfer ? postMessage(message, transfer) : postMessage (message);\n  };\n}\nelse if (typeof process !== 'undefined') {\n  // node.js\n\n  var WorkerThreads;\n  try {\n    WorkerThreads = require('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads, fallback to sub-process based workers\n    } else {\n      throw error;\n    }\n  }\n\n  if (WorkerThreads &&\n    /* if there is a parentPort, we are in a WorkerThread */\n    WorkerThreads.parentPort !== null) {\n    var parentPort  = WorkerThreads.parentPort;\n    worker.send = parentPort.postMessage.bind(parentPort);\n    worker.on = parentPort.on.bind(parentPort);\n    worker.exit = process.exit.bind(process);\n  } else {\n    worker.on = process.on.bind(process);\n    // ignore transfer argument since it is not supported by process\n    worker.send = function (message) {\n      process.send(message);\n    };\n    // register disconnect handler only for subprocess worker to exit when parent is killed unexpectedly\n    worker.on('disconnect', function () {\n      process.exit(1);\n    });\n    worker.exit = process.exit.bind(process);\n  }\n}\nelse {\n  throw new Error('Script must be executed as a worker');\n}\n\nfunction convertError(error) {\n  return Object.getOwnPropertyNames(error).reduce(function(product, name) {\n    return Object.defineProperty(product, name, {\n\tvalue: error[name],\n\tenumerable: true\n    });\n  }, {});\n}\n\n/**\n * Test whether a value is a Promise via duck typing.\n * @param {*} value\n * @returns {boolean} Returns true when given value is an object\n *                    having functions `then` and `catch`.\n */\nfunction isPromise(value) {\n  return value && (typeof value.then === 'function') && (typeof value.catch === 'function');\n}\n\n// functions available externally\nworker.methods = {};\n\n/**\n * Execute a function with provided arguments\n * @param {String} fn     Stringified function\n * @param {Array} [args]  Function arguments\n * @returns {*}\n */\nworker.methods.run = function run(fn, args) {\n  var f = new Function('return (' + fn + ').apply(this, arguments);');\n  f.worker = publicWorker;\n  return f.apply(f, args);\n};\n\n/**\n * Get a list with methods available on this worker\n * @return {String[]} methods\n */\nworker.methods.methods = function methods() {\n  return Object.keys(worker.methods);\n};\n\n/**\n * Custom handler for when the worker is terminated.\n */\nworker.terminationHandler = undefined;\n\nworker.abortListenerTimeout = TIMEOUT_DEFAULT;\n\n/**\n * Abort handlers for resolving errors which may cause a timeout or cancellation\n * to occur from a worker context\n */\nworker.abortListeners = [];\n\n/**\n * Cleanup and exit the worker.\n * @param {Number} code \n * @returns {Promise<void>}\n */\nworker.terminateAndExit = function(code) {\n  var _exit = function() {\n    worker.exit(code);\n  }\n\n  if(!worker.terminationHandler) {\n    return _exit();\n  }\n  \n  var result = worker.terminationHandler(code);\n  if (isPromise(result)) {\n    result.then(_exit, _exit);\n\n    return result;\n  } else {\n    _exit();\n    return new Promise(function (_resolve, reject) {\n      reject(new Error(\"Worker terminating\"));\n    });\n  }\n}\n\n\n\n/**\n  * Called within the worker message handler to run abort handlers if registered to perform cleanup operations.\n  * @param {Integer} [requestId] id of task which is currently executing in the worker\n  * @return {Promise<void>}\n*/\nworker.cleanup = function(requestId) {\n\n  if (!worker.abortListeners.length) {\n    worker.send({\n      id: requestId,\n      method: CLEANUP_METHOD_ID,\n      error: convertError(new Error('Worker terminating')),\n    });\n\n    // If there are no handlers registered, reject the promise with an error as we want the handler to be notified\n    // that cleanup should begin and the handler should be GCed.\n    return new Promise(function(resolve) { resolve(); });\n  }\n  \n\n  var _exit = function() {\n    worker.exit();\n  }\n\n  var _abort = function() {\n    if (!worker.abortListeners.length) {\n      worker.abortListeners = [];\n    }\n  }\n\n  const promises = worker.abortListeners.map(listener => listener());\n  let timerId;\n  const timeoutPromise = new Promise((_resolve, reject) => {\n    timerId = setTimeout(function () { \n      reject(new Error('Timeout occured waiting for abort handler, killing worker'));\n    }, worker.abortListenerTimeout);\n  });\n\n  // Once a promise settles we need to clear the timeout to prevet fulfulling the promise twice \n  const settlePromise = Promise.all(promises).then(function() {\n    clearTimeout(timerId);\n    _abort();\n  }, function() {\n    clearTimeout(timerId);\n    _exit();\n  });\n\n  // Returns a promise which will result in one of the following cases\n  // - Resolve once all handlers resolve\n  // - Reject if one or more handlers exceed the 'abortListenerTimeout' interval\n  // - Reject if one or more handlers reject\n  // Upon one of the above cases a message will be sent to the handler with the result of the handler execution\n  // which will either kill the worker if the result contains an error, or keep it in the pool if the result\n  // does not contain an error.\n  return new Promise(function(resolve, reject) {\n    settlePromise.then(resolve, reject);\n    timeoutPromise.then(resolve, reject);\n  }).then(function() {\n    worker.send({\n      id: requestId,\n      method: CLEANUP_METHOD_ID,\n      error: null,\n    });\n  }, function(err) {\n    worker.send({\n      id: requestId,\n      method: CLEANUP_METHOD_ID,\n      error: err ? convertError(err) : null,\n    });\n  });\n}\n\nvar currentRequestId = null;\n\nworker.on('message', function (request) {\n  if (request === TERMINATE_METHOD_ID) {\n    return worker.terminateAndExit(0);\n  }\n\n  if (request.method === CLEANUP_METHOD_ID) {\n    return worker.cleanup(request.id);\n  }\n\n  try {\n    var method = worker.methods[request.method];\n\n    if (method) {\n      currentRequestId = request.id;\n      \n      // execute the function\n      var result = method.apply(method, request.params);\n\n      if (isPromise(result)) {\n        // promise returned, resolve this and then return\n        result\n            .then(function (result) {\n              if (result instanceof Transfer) {\n                worker.send({\n                  id: request.id,\n                  result: result.message,\n                  error: null\n                }, result.transfer);\n              } else {\n                worker.send({\n                  id: request.id,\n                  result: result,\n                  error: null\n                });\n              }\n              currentRequestId = null;\n            })\n            .catch(function (err) {\n              worker.send({\n                id: request.id,\n                result: null,\n                error: convertError(err),\n              });\n              currentRequestId = null;\n            });\n      }\n      else {\n        // immediate result\n        if (result instanceof Transfer) {\n          worker.send({\n            id: request.id,\n            result: result.message,\n            error: null\n          }, result.transfer);\n        } else {\n          worker.send({\n            id: request.id,\n            result: result,\n            error: null\n          });\n        }\n\n        currentRequestId = null;\n      }\n    }\n    else {\n      throw new Error('Unknown method \"' + request.method + '\"');\n    }\n  }\n  catch (err) {\n    worker.send({\n      id: request.id,\n      result: null,\n      error: convertError(err)\n    });\n  }\n});\n\n/**\n * Register methods to the worker\n * @param {Object} [methods]\n * @param {import('./types.js').WorkerRegisterOptions} [options]\n */\nworker.register = function (methods, options) {\n\n  if (methods) {\n    for (var name in methods) {\n      if (methods.hasOwnProperty(name)) {\n        worker.methods[name] = methods[name];\n        worker.methods[name].worker = publicWorker;\n      }\n    }\n  }\n\n  if (options) {\n    worker.terminationHandler = options.onTerminate;\n    // register listener timeout or default to 1 second\n    worker.abortListenerTimeout = options.abortListenerTimeout || TIMEOUT_DEFAULT;\n  }\n\n  worker.send('ready');\n};\n\nworker.emit = function (payload) {\n  if (currentRequestId) {\n    if (payload instanceof Transfer) {\n      worker.send({\n        id: currentRequestId,\n        isEvent: true,\n        payload: payload.message\n      }, payload.transfer);\n      return;\n    }\n\n    worker.send({\n      id: currentRequestId,\n      isEvent: true,\n      payload\n    });\n  }\n};\n\n\nif (typeof exports !== 'undefined') {\n  exports.add = worker.register;\n  exports.emit = worker.emit;\n}\n"], "names": ["transfer", "message", "this", "Promise", "handler", "parent", "me", "SyntaxError", "_onSuccess", "_onFail", "resolved", "rejected", "pending", "Symbol", "toStringTag", "_process", "onSuccess", "onFail", "push", "then", "resolve", "reject", "s", "_then", "f", "_resolve", "result", "for<PERSON>ach", "fn", "_reject", "error", "cancel", "CancellationError", "timeout", "delay", "timer", "setTimeout", "TimeoutError", "always", "clearTimeout", "callback", "res", "stack", "Error", "prototype", "finally", "final", "all", "promises", "remaining", "length", "results", "p", "i", "defer", "resolver", "promise", "constructor", "name", "_Promise", "Transfer", "require$$0", "require$$1", "CLEANUP_METHOD_ID", "worker", "exit", "publicWorker", "addAbortListener", "listener", "abortListeners", "emit", "self", "postMessage", "addEventListener", "on", "event", "data", "send", "process", "WorkerThreads", "require", "_typeof", "code", "parentPort", "bind", "convertError", "Object", "getOwnPropertyNames", "reduce", "product", "defineProperty", "value", "enumerable", "isPromise", "catch", "methods", "run", "args", "Function", "apply", "keys", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "abortListenerTimeout", "terminateAndExit", "_exit", "cleanup", "requestId", "id", "method", "timerId", "map", "timeoutPromise", "settlePromise", "err", "currentRequestId", "request", "params", "register", "options", "hasOwnProperty", "onTerminate", "payload", "isEvent", "exports", "add"], "mappings": "0jBAWA,IAAAA,EALA,SAAkBC,EAASD,GACzBE,KAAKD,QAAUA,EACfC,KAAKF,SAAWA,CAClB,OCEA,SAASG,EAAQC,EAASC,GACxB,IAAIC,EAAKJ,KAET,KAAMA,gBAAgBC,GACpB,MAAM,IAAII,YAAY,oDAGxB,GAAuB,mBAAZH,EACT,MAAM,IAAIG,YAAY,uDAGxB,IAAIC,EAAa,GACbC,EAAU,GAMdP,KAAKQ,UAAW,EAIhBR,KAAKS,UAAW,EAIhBT,KAAKU,SAAU,EAIfV,KAAKW,OAAOC,aAAe,UAS3B,IAAIC,EAAW,SAAUC,EAAWC,GAClCT,EAAWU,KAAKF,GAChBP,EAAQS,KAAKD,IAWff,KAAKiB,KAAO,SAAUH,EAAWC,GAC/B,OAAO,IAAId,GAAQ,SAAUiB,EAASC,GACpC,IAAIC,EAAIN,EAAYO,EAAMP,EAAWI,EAASC,GAAUD,EACpDI,EAAIP,EAAYM,EAAMN,EAAWG,EAASC,GAAUA,EAExDN,EAASO,EAAGE,KACXlB,IAQL,IAAImB,EAAW,SAAUC,GAgBvB,OAdApB,EAAGI,UAAW,EACdJ,EAAGK,UAAW,EACdL,EAAGM,SAAU,EAEbJ,EAAWmB,SAAQ,SAAUC,GAC3BA,EAAGF,EACT,IAEIX,EAAW,SAAUC,EAAWC,GAC9BD,EAAUU,IAGZD,EAAWI,EAAU,WAAY,EAE1BvB,GAQLuB,EAAU,SAAUC,GAgBtB,OAdAxB,EAAGI,UAAW,EACdJ,EAAGK,UAAW,EACdL,EAAGM,SAAU,EAEbH,EAAQkB,SAAQ,SAAUC,GACxBA,EAAGE,EACT,IAEIf,EAAW,SAAUC,EAAWC,GAC9BA,EAAOa,IAGTL,EAAWI,EAAU,WAAY,EAE1BvB,GAOTJ,KAAK6B,OAAS,WAQZ,OAPI1B,EACFA,EAAO0B,SAGPF,EAAQ,IAAIG,GAGP1B,GAUTJ,KAAK+B,QAAU,SAAUC,GACvB,GAAI7B,EACFA,EAAO4B,QAAQC,OAEZ,CACH,IAAIC,EAAQC,YAAW,WACrBP,EAAQ,IAAIQ,EAAa,2BAA6BH,EAAQ,UAC7DA,GAEH5B,EAAGgC,QAAO,WACRC,aAAaJ,EACrB,GACA,CAEI,OAAO7B,GAITF,GAAQ,SAAUsB,GAChBD,EAASC,MACR,SAAUI,GACXD,EAAQC,EACZ,GACA,CAUA,SAASP,EAAMiB,EAAUpB,EAASC,GAChC,OAAO,SAAUK,GACf,IACE,IAAIe,EAAMD,EAASd,GACfe,GAA2B,mBAAbA,EAAItB,MAA+C,mBAAjBsB,EAAW,MAE7DA,EAAItB,KAAKC,EAASC,GAGlBD,EAAQqB,GAGZ,MAAOX,GACLT,EAAOS,EACb,EAEA,CA8FA,SAASE,EAAkB/B,GACzBC,KAAKD,QAAUA,GAAW,oBAC1BC,KAAKwC,OAAS,IAAIC,OAASD,KAC7B,CAcA,SAASL,EAAapC,GACpBC,KAAKD,QAAUA,GAAW,mBAC1BC,KAAKwC,OAAS,IAAIC,OAASD,KAC7B,QA1GAvC,EAAQyC,UAAiB,MAAI,SAAU3B,GACrC,OAAOf,KAAKiB,KAAK,KAAMF,EACzB,EAWAd,EAAQyC,UAAUN,OAAS,SAAUV,GACnC,OAAO1B,KAAKiB,KAAKS,EAAIA,EACvB,EAQAzB,EAAQyC,UAAUC,QAAU,SAAUjB,GACpC,IAAMtB,EAAKJ,KAEL4C,EAAQ,WACZ,OAAO,IAAI3C,GAAQ,SAACiB,GAAO,OAAKA,GAAS,IACtCD,KAAKS,GACLT,MAAK,WAAA,OAAMb,MAGhB,OAAOJ,KAAKiB,KAAK2B,EAAOA,EAC1B,EAQA3C,EAAQ4C,IAAM,SAAUC,GACtB,OAAO,IAAI7C,GAAQ,SAAUiB,EAASC,GACpC,IAAI4B,EAAYD,EAASE,OACrBC,EAAU,GAEVF,EACFD,EAASrB,SAAQ,SAAUyB,EAAGC,GAC5BD,EAAEjC,MAAK,SAAUO,GACfyB,EAAQE,GAAK3B,EAEI,KADjBuB,GAEE7B,EAAQ+B,MAET,SAAUrB,GACXmB,EAAY,EACZ5B,EAAOS,EACjB,GACA,IAGMV,EAAQ+B,EAEd,GACA,EAMAhD,EAAQmD,MAAQ,WACd,IAAIC,EAAW,CAAA,EAOf,OALAA,EAASC,QAAU,IAAIrD,GAAQ,SAAUiB,EAASC,GAChDkC,EAASnC,QAAUA,EACnBmC,EAASlC,OAASA,CACtB,IAESkC,CACT,EAYAvB,EAAkBY,UAAY,IAAID,MAClCX,EAAkBY,UAAUa,YAAcd,MAC1CX,EAAkBY,UAAUc,KAAO,oBAEnCvD,EAAQ6B,kBAAoBA,EAa5BK,EAAaO,UAAY,IAAID,MAC7BN,EAAaO,UAAUa,YAAcd,MACrCN,EAAaO,UAAUc,KAAO,eAE9BvD,EAAQkC,aAAeA,EAGvBsB,EAAAxD,QAAkBA,cCrTlB,IAAIyD,EAAWC,EAKX1D,EAAU2D,EAAqB3D,QAW/B4D,EAAoB,yBAQpBC,EAAS,CACXC,KAAM,WAAW,GAKfC,EAAe,CAMjBC,iBAAkB,SAASC,GACzBJ,EAAOK,eAAenD,KAAKkD,IAO7BE,KAAMN,EAAOM,MAGf,GAAoB,oBAATC,MAA+C,mBAAhBC,aAA0D,mBAArBC,iBAE7ET,EAAOU,GAAK,SAAUC,EAAOnC,GAC3BiC,iBAAiBE,GAAO,SAAU1E,GAChCuC,EAASvC,EAAQ2E,KACvB,KAEEZ,EAAOa,KAAO,SAAU5E,EAASD,GAC9BA,EAAWwE,YAAYvE,EAASD,GAAYwE,YAAavE,QAGzD,IAAuB,oBAAZ6E,QAmCd,MAAM,IAAInC,MAAM,uCAhChB,IAAIoC,EACJ,IACEA,EAAgBC,QAAQ,kBACxB,MAAMlD,GACN,GAAqB,WAAjBmD,EAAOnD,IAAgC,OAAVA,GAAiC,qBAAfA,EAAMoD,KAGvD,MAAMpD,CAEZ,CAEE,GAAIiD,GAE2B,OAA7BA,EAAcI,WAAqB,CACnC,IAAIA,EAAcJ,EAAcI,WAChCnB,EAAOa,KAAOM,EAAWX,YAAYY,KAAKD,GAC1CnB,EAAOU,GAAKS,EAAWT,GAAGU,KAAKD,GAC/BnB,EAAOC,KAAOa,QAAQb,KAAKmB,KAAKN,QACpC,MACId,EAAOU,GAAKI,QAAQJ,GAAGU,KAAKN,SAE5Bd,EAAOa,KAAO,SAAU5E,GACtB6E,QAAQD,KAAK5E,IAGf+D,EAAOU,GAAG,cAAc,WACtBI,QAAQb,KAAK,EACnB,IACID,EAAOC,KAAOa,QAAQb,KAAKmB,KAAKN,QAKpC,CAEA,SAASO,EAAavD,GACpB,OAAOwD,OAAOC,oBAAoBzD,GAAO0D,QAAO,SAASC,EAAS/B,GAChE,OAAO4B,OAAOI,eAAeD,EAAS/B,EAAM,CAC/CiC,MAAO7D,EAAM4B,GACbkC,YAAY,MAER,GACL,CAQA,SAASC,EAAUF,GACjB,OAAOA,GAAgC,mBAAfA,EAAMxE,MAAgD,mBAAhBwE,EAAMG,KACtE,CAGA9B,EAAO+B,QAAU,CAAA,EAQjB/B,EAAO+B,QAAQC,IAAM,SAAapE,EAAIqE,GACpC,IAAIzE,EAAI,IAAI0E,SAAS,WAAatE,EAAK,6BAEvC,OADAJ,EAAEwC,OAASE,EACJ1C,EAAE2E,MAAM3E,EAAGyE,IAOpBjC,EAAO+B,QAAQA,QAAU,WACvB,OAAOT,OAAOc,KAAKpC,EAAO+B,UAM5B/B,EAAOqC,wBAAqBC,EAE5BtC,EAAOuC,qBA3He,IAiItBvC,EAAOK,eAAiB,GAOxBL,EAAOwC,iBAAmB,SAAStB,GACjC,IAAIuB,EAAQ,WACVzC,EAAOC,KAAKiB,IAGd,IAAIlB,EAAOqC,mBACT,OAAOI,IAGT,IAAI/E,EAASsC,EAAOqC,mBAAmBnB,GACvC,OAAIW,EAAUnE,IACZA,EAAOP,KAAKsF,EAAOA,GAEZ/E,IAEP+E,IACO,IAAItG,GAAQ,SAAUsB,EAAUJ,GACrCA,EAAO,IAAIsB,MAAM,sBACvB,MAWAqB,EAAO0C,QAAU,SAASC,GAExB,IAAK3C,EAAOK,eAAenB,OASzB,OARAc,EAAOa,KAAK,CACV+B,GAAID,EACJE,OAAQ9C,EACRjC,MAAOuD,EAAa,IAAI1C,MAAM,yBAKzB,IAAIxC,GAAQ,SAASiB,GAAWA,GAAU,IAInD,IAWI0F,EADE9D,EAAWgB,EAAOK,eAAe0C,KAAI,SAAA3C,GAAQ,OAAIA,OAEjD4C,EAAiB,IAAI7G,GAAQ,SAACsB,EAAUJ,GAC5CyF,EAAU1E,YAAW,WACnBf,EAAO,IAAIsB,MAAM,6DACvB,GAAOqB,EAAOuC,qBACd,IAGQU,EAAgB9G,EAAQ4C,IAAIC,GAAU7B,MAAK,WAC/CoB,aAAauE,GAfR9C,EAAOK,eAAenB,SACzBc,EAAOK,eAAiB,GAgB9B,IAAK,WACD9B,aAAauE,GAtBb9C,EAAOC,MAwBX,IASE,OAAO,IAAI9D,GAAQ,SAASiB,EAASC,GACnC4F,EAAc9F,KAAKC,EAASC,GAC5B2F,EAAe7F,KAAKC,EAASC,EACjC,IAAKF,MAAK,WACN6C,EAAOa,KAAK,CACV+B,GAAID,EACJE,OAAQ9C,EACRjC,MAAO,UAER,SAASoF,GACVlD,EAAOa,KAAK,CACV+B,GAAID,EACJE,OAAQ9C,EACRjC,MAAOoF,EAAM7B,EAAa6B,GAAO,MAEvC,KAGA,IAAIC,EAAmB,KAEvBnD,EAAOU,GAAG,WAAW,SAAU0C,GAC7B,GArPwB,6BAqPpBA,EACF,OAAOpD,EAAOwC,iBAAiB,GAGjC,GAAIY,EAAQP,SAAW9C,EACrB,OAAOC,EAAO0C,QAAQU,EAAQR,IAGhC,IACE,IAAIC,EAAS7C,EAAO+B,QAAQqB,EAAQP,QAEpC,IAAIA,EAsDF,MAAM,IAAIlE,MAAM,mBAAqByE,EAAQP,OAAS,KArDtDM,EAAmBC,EAAQR,GAG3B,IAAIlF,EAASmF,EAAOV,MAAMU,EAAQO,EAAQC,QAEtCxB,EAAUnE,GAEZA,EACKP,MAAK,SAAUO,GACVA,aAAkBkC,EACpBI,EAAOa,KAAK,CACV+B,GAAIQ,EAAQR,GACZlF,OAAQA,EAAOzB,QACf6B,MAAO,MACNJ,EAAO1B,UAEVgE,EAAOa,KAAK,CACV+B,GAAIQ,EAAQR,GACZlF,OAAQA,EACRI,MAAO,OAGXqF,EAAmB,IACjC,IACarB,OAAM,SAAUoB,GACflD,EAAOa,KAAK,CACV+B,GAAIQ,EAAQR,GACZlF,OAAQ,KACRI,MAAOuD,EAAa6B,KAEtBC,EAAmB,IACjC,KAIYzF,aAAkBkC,EACpBI,EAAOa,KAAK,CACV+B,GAAIQ,EAAQR,GACZlF,OAAQA,EAAOzB,QACf6B,MAAO,MACNJ,EAAO1B,UAEVgE,EAAOa,KAAK,CACV+B,GAAIQ,EAAQR,GACZlF,OAAQA,EACRI,MAAO,OAIXqF,EAAmB,MAOzB,MAAOD,GACLlD,EAAOa,KAAK,CACV+B,GAAIQ,EAAQR,GACZlF,OAAQ,KACRI,MAAOuD,EAAa6B,IAE1B,CACA,IAOAlD,EAAOsD,SAAW,SAAUvB,EAASwB,GAEnC,GAAIxB,EACF,IAAK,IAAIrC,KAAQqC,EACXA,EAAQyB,eAAe9D,KACzBM,EAAO+B,QAAQrC,GAAQqC,EAAQrC,GAC/BM,EAAO+B,QAAQrC,GAAMM,OAASE,GAKhCqD,IACFvD,EAAOqC,mBAAqBkB,EAAQE,YAEpCzD,EAAOuC,qBAAuBgB,EAAQhB,sBA3UpB,KA8UpBvC,EAAOa,KAAK,UAGdb,EAAOM,KAAO,SAAUoD,GACtB,GAAIP,EAAkB,CACpB,GAAIO,aAAmB9D,EAMrB,YALAI,EAAOa,KAAK,CACV+B,GAAIO,EACJQ,SAAS,EACTD,QAASA,EAAQzH,SAChByH,EAAQ1H,UAIbgE,EAAOa,KAAK,CACV+B,GAAIO,EACJQ,SAAS,EACTD,QAAAA,GAEN,GAKEE,EAAAC,IAAc7D,EAAOsD,SACrBM,EAAAtD,KAAeN,EAAOM"}