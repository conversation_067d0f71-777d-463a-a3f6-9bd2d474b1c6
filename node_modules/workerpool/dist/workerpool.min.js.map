{"version": 3, "file": "workerpool.min.js", "sources": ["../src/environment.js", "../src/Promise.js", "../src/validateOptions.js", "../src/WorkerHandler.js", "../src/generated/embeddedWorker.js", "../src/Pool.js", "../src/debug-port-allocator.js", "../src/transfer.js", "../src/worker.js", "../src/index.js"], "sourcesContent": ["\n// source: https://github.com/flexdinesh/browser-or-node\n// source: https://github.com/mozilla/pdf.js/blob/7ea0e40e588864cd938d1836ec61f1928d3877d3/src/shared/util.js#L24\nvar isNode = function (nodeProcess) {\n  return (\n    typeof nodeProcess !== 'undefined' &&\n    nodeProcess.versions != null &&\n    nodeProcess.versions.node != null &&\n    nodeProcess + '' === '[object process]'\n  );\n}\nmodule.exports.isNode = isNode\n\n// determines the JavaScript platform: browser or node\nmodule.exports.platform = typeof process !== 'undefined' && isNode(process)\n  ? 'node'\n  : 'browser';\n\n// determines whether the code is running in main thread or not\n// note that in node.js we have to check both worker_thread and child_process\nvar worker_threads = module.exports.platform === 'node' && require('worker_threads');\nmodule.exports.isMainThread = module.exports.platform === 'node'\n  ? ((!worker_threads || worker_threads.isMainThread) && !process.connected)\n  : typeof Window !== 'undefined';\n\n// determines the number of cpus available\nmodule.exports.cpus = module.exports.platform === 'browser'\n  ? self.navigator.hardwareConcurrency\n  : require('os').cpus().length;\n\n", "'use strict';\n\n/**\n * Promise\n *\n * Inspired by https://gist.github.com/RubaXa/8501359 from RubaXa <<EMAIL>>\n * @template T\n * @template [E=Error]\n * @param {Function} handler   Called as handler(resolve: Function, reject: Function)\n * @param {Promise} [parent]   Parent promise for propagation of cancel and timeout\n */\nfunction Promise(handler, parent) {\n  var me = this;\n\n  if (!(this instanceof Promise)) {\n    throw new SyntaxError('Constructor must be called with the new operator');\n  }\n\n  if (typeof handler !== 'function') {\n    throw new SyntaxError('Function parameter handler(resolve, reject) missing');\n  }\n\n  var _onSuccess = [];\n  var _onFail = [];\n\n  // status\n  /**\n   * @readonly\n   */\n  this.resolved = false;\n  /**\n   * @readonly\n   */\n  this.rejected = false;\n  /**\n   * @readonly\n   */\n  this.pending = true;\n  /**\n   * @readonly\n   */\n  this[Symbol.toStringTag] = 'Promise';\n\n  /**\n   * Process onSuccess and onFail callbacks: add them to the queue.\n   * Once the promise is resolved, the function _promise is replace.\n   * @param {Function} onSuccess\n   * @param {Function} onFail\n   * @private\n   */\n  var _process = function (onSuccess, onFail) {\n    _onSuccess.push(onSuccess);\n    _onFail.push(onFail);\n  };\n\n  /**\n   * Add an onSuccess callback and optionally an onFail callback to the Promise\n   * @template TT\n   * @template [TE=never]\n   * @param {(r: T) => TT | PromiseLike<TT>} onSuccess\n   * @param {(r: E) => TE | PromiseLike<TE>} [onFail]\n   * @returns {Promise<TT | TE, any>} promise\n   */\n  this.then = function (onSuccess, onFail) {\n    return new Promise(function (resolve, reject) {\n      var s = onSuccess ? _then(onSuccess, resolve, reject) : resolve;\n      var f = onFail    ? _then(onFail,    resolve, reject) : reject;\n\n      _process(s, f);\n    }, me);\n  };\n\n  /**\n   * Resolve the promise\n   * @param {*} result\n   * @type {Function}\n   */\n  var _resolve = function (result) {\n    // update status\n    me.resolved = true;\n    me.rejected = false;\n    me.pending = false;\n\n    _onSuccess.forEach(function (fn) {\n      fn(result);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onSuccess(result);\n    };\n\n    _resolve = _reject = function () { };\n\n    return me;\n  };\n\n  /**\n   * Reject the promise\n   * @param {Error} error\n   * @type {Function}\n   */\n  var _reject = function (error) {\n    // update status\n    me.resolved = false;\n    me.rejected = true;\n    me.pending = false;\n\n    _onFail.forEach(function (fn) {\n      fn(error);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onFail(error);\n    };\n\n    _resolve = _reject = function () { }\n\n    return me;\n  };\n\n  /**\n   * Cancel the promise. This will reject the promise with a CancellationError\n   * @returns {this} self\n   */\n  this.cancel = function () {\n    if (parent) {\n      parent.cancel();\n    }\n    else {\n      _reject(new CancellationError());\n    }\n\n    return me;\n  };\n\n  /**\n   * Set a timeout for the promise. If the promise is not resolved within\n   * the time, the promise will be cancelled and a TimeoutError is thrown.\n   * If the promise is resolved in time, the timeout is removed.\n   * @param {number} delay     Delay in milliseconds\n   * @returns {this} self\n   */\n  this.timeout = function (delay) {\n    if (parent) {\n      parent.timeout(delay);\n    }\n    else {\n      var timer = setTimeout(function () {\n        _reject(new TimeoutError('Promise timed out after ' + delay + ' ms'));\n      }, delay);\n\n      me.always(function () {\n        clearTimeout(timer);\n      });\n    }\n\n    return me;\n  };\n\n  // attach handler passing the resolve and reject functions\n  handler(function (result) {\n    _resolve(result);\n  }, function (error) {\n    _reject(error);\n  });\n}\n\n/**\n * Execute given callback, then call resolve/reject based on the returned result\n * @param {Function} callback\n * @param {Function} resolve\n * @param {Function} reject\n * @returns {Function}\n * @private\n */\nfunction _then(callback, resolve, reject) {\n  return function (result) {\n    try {\n      var res = callback(result);\n      if (res && typeof res.then === 'function' && typeof res['catch'] === 'function') {\n        // method returned a promise\n        res.then(resolve, reject);\n      }\n      else {\n        resolve(res);\n      }\n    }\n    catch (error) {\n      reject(error);\n    }\n  }\n}\n\n/**\n * Add an onFail callback to the Promise\n * @template TT\n * @param {(error: E) => TT | PromiseLike<TT>} onFail\n * @returns {Promise<T | TT>} promise\n */\nPromise.prototype['catch'] = function (onFail) {\n  return this.then(null, onFail);\n};\n\n// TODO: add support for Promise.catch(Error, callback)\n// TODO: add support for Promise.catch(Error, Error, callback)\n\n/**\n * Execute given callback when the promise either resolves or rejects.\n * @template TT\n * @param {() => Promise<TT>} fn\n * @returns {Promise<TT>} promise\n */\nPromise.prototype.always = function (fn) {\n  return this.then(fn, fn);\n};\n\n/**\n  * Execute given callback when the promise either resolves or rejects.\n  * Same semantics as Node's Promise.finally()\n  * @param {Function | null | undefined} [fn]\n  * @returns {Promise} promise\n  */\nPromise.prototype.finally = function (fn) {\n  const me = this;\n\n  const final = function() {\n    return new Promise((resolve) => resolve())\n      .then(fn)\n      .then(() => me);\n  };\n\n  return this.then(final, final);\n}\n\n/**\n * Create a promise which resolves when all provided promises are resolved,\n * and fails when any of the promises resolves.\n * @param {Promise[]} promises\n * @returns {Promise<any[], any>} promise\n */\nPromise.all = function (promises){\n  return new Promise(function (resolve, reject) {\n    var remaining = promises.length,\n        results = [];\n\n    if (remaining) {\n      promises.forEach(function (p, i) {\n        p.then(function (result) {\n          results[i] = result;\n          remaining--;\n          if (remaining == 0) {\n            resolve(results);\n          }\n        }, function (error) {\n          remaining = 0;\n          reject(error);\n        });\n      });\n    }\n    else {\n      resolve(results);\n    }\n  });\n};\n\n/**\n * Create a promise resolver\n * @returns {{promise: Promise, resolve: Function, reject: Function}} resolver\n */\nPromise.defer = function () {\n  var resolver = {};\n\n  resolver.promise = new Promise(function (resolve, reject) {\n    resolver.resolve = resolve;\n    resolver.reject = reject;\n  });\n\n  return resolver;\n};\n\n/**\n * Create a cancellation error\n * @param {String} [message]\n * @extends Error\n */\nfunction CancellationError(message) {\n  this.message = message || 'promise cancelled';\n  this.stack = (new Error()).stack;\n}\n\nCancellationError.prototype = new Error();\nCancellationError.prototype.constructor = Error;\nCancellationError.prototype.name = 'CancellationError';\n\nPromise.CancellationError = CancellationError;\n\n\n/**\n * Create a timeout error\n * @param {String} [message]\n * @extends Error\n */\nfunction TimeoutError(message) {\n  this.message = message || 'timeout exceeded';\n  this.stack = (new Error()).stack;\n}\n\nTimeoutError.prototype = new Error();\nTimeoutError.prototype.constructor = Error;\nTimeoutError.prototype.name = 'TimeoutError';\n\nPromise.TimeoutError = TimeoutError;\n\n\nexports.Promise = Promise;\n", "/**\n * Validate that the object only contains known option names\n * - Throws an error when unknown options are detected\n * - Throws an error when some of the allowed options are attached\n * @param {Object | undefined} options\n * @param {string[]} allowedOptionNames\n * @param {string} objectName\n * @retrun {Object} Returns the original options\n */\nexports.validateOptions = function validateOptions(options, allowedOptionNames, objectName) {\n  if (!options) {\n    return\n  }\n\n  var optionNames = options ?  Object.keys(options) : []\n\n  // check for unknown properties\n  var unknownOptionName = optionNames.find(optionName => !allowedOptionNames.includes(optionName))\n  if (unknownOptionName) {\n    throw new Error('Object \"' + objectName + '\" contains an unknown option \"' + unknownOptionName + '\"')\n  }\n\n  // check for inherited properties which are not present on the object itself\n  var illegalOptionName = allowedOptionNames.find(allowedOptionName => {\n    return Object.prototype[allowedOptionName] && !optionNames.includes(allowedOptionName)\n  })\n  if (illegalOptionName) {\n    throw new Error('Object \"' + objectName + '\" contains an inherited option \"' + illegalOptionName + '\" which is ' +\n      'not defined in the object itself but in its prototype. Only plain objects are allowed. ' +\n      'Please remove the option from the prototype or override it with a value \"undefined\".')\n  }\n\n  return options\n}\n\n// source: https://developer.mozilla.org/en-US/docs/Web/API/Worker/Worker\nexports.workerOptsNames = [\n  'credentials', 'name', 'type' ]\n\n// source: https://nodejs.org/api/child_process.html#child_processforkmodulepath-args-options\nexports.forkOptsNames = [\n  'cwd', 'detached', 'env', 'execPath', 'execArgv', 'gid', 'serialization',\n  'signal', 'killSignal', 'silent', 'stdio', 'uid', 'windowsVerbatimArguments',\n  'timeout'\n]\n\n// source: https://nodejs.org/api/worker_threads.html#new-workerfilename-options\nexports.workerThreadOptsNames = [\n  'argv', 'env', 'eval', 'execArgv', 'stdin', 'stdout', 'stderr', 'workerData',\n  'trackUnmanagedFds', 'transferList', 'resourceLimits', 'name'\n]\n", "'use strict';\n\nvar {Promise} = require('./Promise');\nvar environment = require('./environment');\nconst {validateOptions, forkOptsNames, workerThreadOptsNames, workerOptsNames} = require(\"./validateOptions\");\n\n/**\n * Special message sent by parent which causes a child process worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\n/**\n * Special message by parent which causes a child process worker to perform cleaup\n * steps before determining if the child process worker should be terminated.\n */\nvar CLEANUP_METHOD_ID = '__workerpool-cleanup__';\n\nfunction ensureWorkerThreads() {\n  var WorkerThreads = tryRequireWorkerThreads()\n  if (!WorkerThreads) {\n    throw new Error('WorkerPool: workerType = \\'thread\\' is not supported, Node >= 11.7.0 required')\n  }\n\n  return WorkerThreads;\n}\n\n// check whether Worker is supported by the browser\nfunction ensureWebWorker() {\n  // Workaround for a bug in PhantomJS (Or QtWebkit): https://github.com/ariya/phantomjs/issues/14534\n  if (typeof Worker !== 'function' && (typeof Worker !== 'object' || typeof Worker.prototype.constructor !== 'function')) {\n    throw new Error('WorkerPool: Web Workers not supported');\n  }\n}\n\nfunction tryRequireWorkerThreads() {\n  try {\n    return require('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads available (old version of node.js)\n      return null;\n    } else {\n      throw error;\n    }\n  }\n}\n\n// get the default worker script\nfunction getDefaultWorker() {\n  if (environment.platform === 'browser') {\n    // test whether the browser supports all features that we need\n    if (typeof Blob === 'undefined') {\n      throw new Error('Blob not supported by the browser');\n    }\n    if (!window.URL || typeof window.URL.createObjectURL !== 'function') {\n      throw new Error('URL.createObjectURL not supported by the browser');\n    }\n\n    // use embedded worker.js\n    var blob = new Blob([require('./generated/embeddedWorker')], {type: 'text/javascript'});\n    return window.URL.createObjectURL(blob);\n  }\n  else {\n    // use external worker.js in current directory\n    return __dirname + '/worker.js';\n  }\n}\n\nfunction setupWorker(script, options) {\n  if (options.workerType === 'web') { // browser only\n    ensureWebWorker();\n    return setupBrowserWorker(script, options.workerOpts, Worker);\n  } else if (options.workerType === 'thread') { // node.js only\n    WorkerThreads = ensureWorkerThreads();\n    return setupWorkerThreadWorker(script, WorkerThreads, options);\n  } else if (options.workerType === 'process' || !options.workerType) { // node.js only\n    return setupProcessWorker(script, resolveForkOptions(options), require('child_process'));\n  } else { // options.workerType === 'auto' or undefined\n    if (environment.platform === 'browser') {\n      ensureWebWorker();\n      return setupBrowserWorker(script, options.workerOpts, Worker);\n    }\n    else { // environment.platform === 'node'\n      var WorkerThreads = tryRequireWorkerThreads();\n      if (WorkerThreads) {\n        return setupWorkerThreadWorker(script, WorkerThreads, options);\n      } else {\n        return setupProcessWorker(script, resolveForkOptions(options), require('child_process'));\n      }\n    }\n  }\n}\n\nfunction setupBrowserWorker(script, workerOpts, Worker) {\n  // validate the options right before creating the worker (not when creating the pool)\n  validateOptions(workerOpts, workerOptsNames, 'workerOpts')\n\n  // create the web worker\n  var worker = new Worker(script, workerOpts);\n\n  worker.isBrowserWorker = true;\n  // add node.js API to the web worker\n  worker.on = function (event, callback) {\n    this.addEventListener(event, function (message) {\n      callback(message.data);\n    });\n  };\n  worker.send = function (message, transfer) {\n    this.postMessage(message, transfer);\n  };\n  return worker;\n}\n\nfunction setupWorkerThreadWorker(script, WorkerThreads, options) {\n  // validate the options right before creating the worker thread (not when creating the pool)\n  validateOptions(options?.workerThreadOpts, workerThreadOptsNames, 'workerThreadOpts')\n\n  var worker = new WorkerThreads.Worker(script, {\n    stdout: options?.emitStdStreams ?? false, // pipe worker.STDOUT to process.STDOUT if not requested\n    stderr: options?.emitStdStreams ?? false,  // pipe worker.STDERR to process.STDERR if not requested\n    ...options?.workerThreadOpts\n  });\n  worker.isWorkerThread = true;\n  worker.send = function(message, transfer) {\n    this.postMessage(message, transfer);\n  };\n\n  worker.kill = function() {\n    this.terminate();\n    return true;\n  };\n\n  worker.disconnect = function() {\n    this.terminate();\n  };\n\n  if (options?.emitStdStreams) {\n    worker.stdout.on('data', (data) => worker.emit(\"stdout\", data))\n    worker.stderr.on('data', (data) => worker.emit(\"stderr\", data))\n  }\n\n  return worker;\n}\n\nfunction setupProcessWorker(script, options, child_process) {\n  // validate the options right before creating the child process (not when creating the pool)\n  validateOptions(options.forkOpts, forkOptsNames, 'forkOpts')\n\n  // no WorkerThreads, fallback to sub-process based workers\n  var worker = child_process.fork(\n    script,\n    options.forkArgs,\n    options.forkOpts\n  );\n\n  // ignore transfer argument since it is not supported by process\n  var send = worker.send;\n  worker.send = function (message) {\n    return send.call(worker, message);\n  };\n\n  if (options.emitStdStreams) {\n    worker.stdout.on('data', (data) => worker.emit(\"stdout\", data))\n    worker.stderr.on('data', (data) => worker.emit(\"stderr\", data))\n  }\n\n  worker.isChildProcess = true;\n  return worker;\n}\n\n// add debug flags to child processes if the node inspector is active\nfunction resolveForkOptions(opts) {\n  opts = opts || {};\n\n  var processExecArgv = process.execArgv.join(' ');\n  var inspectorActive = processExecArgv.indexOf('--inspect') !== -1;\n  var debugBrk = processExecArgv.indexOf('--debug-brk') !== -1;\n\n  var execArgv = [];\n  if (inspectorActive) {\n    execArgv.push('--inspect=' + opts.debugPort);\n\n    if (debugBrk) {\n      execArgv.push('--debug-brk');\n    }\n  }\n\n  process.execArgv.forEach(function(arg) {\n    if (arg.indexOf('--max-old-space-size') > -1) {\n      execArgv.push(arg)\n    }\n  })\n\n  return Object.assign({}, opts, {\n    forkArgs: opts.forkArgs,\n    forkOpts: Object.assign({}, opts.forkOpts, {\n      execArgv: (opts.forkOpts && opts.forkOpts.execArgv || [])\n      .concat(execArgv),\n      stdio: opts.emitStdStreams ? \"pipe\": undefined\n    })\n  });\n}\n\n/**\n * Converts a serialized error to Error\n * @param {Object} obj Error that has been serialized and parsed to object\n * @return {Error} The equivalent Error.\n */\nfunction objectToError (obj) {\n  var temp = new Error('')\n  var props = Object.keys(obj)\n\n  for (var i = 0; i < props.length; i++) {\n    temp[props[i]] = obj[props[i]]\n  }\n\n  return temp\n}\n\nfunction handleEmittedStdPayload(handler, payload) {\n  // TODO: refactor if parallel task execution gets added\n  Object.values(handler.processing)\n    .forEach(task => task?.options?.on(payload));\n  \n  Object.values(handler.tracking)\n    .forEach(task => task?.options?.on(payload)); \n}\n\n/**\n * A WorkerHandler controls a single worker. This worker can be a child process\n * on node.js or a WebWorker in a browser environment.\n * @param {String} [script] If no script is provided, a default worker with a\n *                          function run will be created.\n * @param {import('./types.js').WorkerPoolOptions} [_options] See docs\n * @constructor\n */\nfunction WorkerHandler(script, _options) {\n  var me = this;\n  var options = _options || {};\n\n  this.script = script || getDefaultWorker();\n  this.worker = setupWorker(this.script, options);\n  this.debugPort = options.debugPort;\n  this.forkOpts = options.forkOpts;\n  this.forkArgs = options.forkArgs;\n  this.workerOpts = options.workerOpts;\n  this.workerThreadOpts = options.workerThreadOpts\n  this.workerTerminateTimeout = options.workerTerminateTimeout;\n\n  // The ready message is only sent if the worker.add method is called (And the default script is not used)\n  if (!script) {\n    this.worker.ready = true;\n  }\n\n  // queue for requests that are received before the worker is ready\n  this.requestQueue = [];\n\n  this.worker.on(\"stdout\", function (data) {\n    handleEmittedStdPayload(me, {\"stdout\": data.toString()})\n  })\n  this.worker.on(\"stderr\", function (data) {\n    handleEmittedStdPayload(me, {\"stderr\": data.toString()})\n  })\n\n  this.worker.on('message', function (response) {\n    if (me.terminated) {\n      return;\n    }\n    if (typeof response === 'string' && response === 'ready') {\n      me.worker.ready = true;\n      dispatchQueuedRequests();\n    } else {\n      // find the task from the processing queue, and run the tasks callback\n      var id = response.id;\n      var task = me.processing[id];\n      if (task !== undefined) {\n        if (response.isEvent) {\n          if (task.options && typeof task.options.on === 'function') {\n            task.options.on(response.payload);\n          }\n        } else {\n          // remove the task from the queue\n          delete me.processing[id];\n\n          // test if we need to terminate\n          if (me.terminating === true) {\n            // complete worker termination if all tasks are finished\n            me.terminate();\n          }\n\n          // resolve the task's promise\n          if (response.error) {\n            task.resolver.reject(objectToError(response.error));\n          }\n          else {\n            task.resolver.resolve(response.result);\n          }\n        }\n      } else {\n        // if the task is not the current, it might be tracked for cleanup\n        var task = me.tracking[id];\n        if (task !== undefined) {\n          if (response.isEvent) {\n            if (task.options && typeof task.options.on === 'function') {\n              task.options.on(response.payload);\n            }\n          }\n        } \n      }\n\n      if (response.method === CLEANUP_METHOD_ID) {\n        var trackedTask = me.tracking[response.id];\n        if (trackedTask !== undefined) {\n          if (response.error) {\n            clearTimeout(trackedTask.timeoutId);\n            trackedTask.resolver.reject(objectToError(response.error))\n          } else {\n            me.tracking && clearTimeout(trackedTask.timeoutId);\n            // if we do not encounter an error wrap the the original timeout error and reject\n            trackedTask.resolver.reject(new WrappedTimeoutError(trackedTask.error));\n          }\n        }\n        delete me.tracking[id];\n      }\n    }\n  });\n\n  // reject all running tasks on worker error\n  function onError(error) {\n    me.terminated = true;\n\n    for (var id in me.processing) {\n      if (me.processing[id] !== undefined) {\n        me.processing[id].resolver.reject(error);\n      }\n    }\n    \n    me.processing = Object.create(null);\n  }\n\n  // send all queued requests to worker\n  function dispatchQueuedRequests()\n  {\n    for(const request of me.requestQueue.splice(0)) {\n      me.worker.send(request.message, request.transfer);\n    }\n  }\n\n  var worker = this.worker;\n  // listen for worker messages error and exit\n  this.worker.on('error', onError);\n  this.worker.on('exit', function (exitCode, signalCode) {\n    var message = 'Workerpool Worker terminated Unexpectedly\\n';\n\n    message += '    exitCode: `' + exitCode + '`\\n';\n    message += '    signalCode: `' + signalCode + '`\\n';\n\n    message += '    workerpool.script: `' +  me.script + '`\\n';\n    message += '    spawnArgs: `' +  worker.spawnargs + '`\\n';\n    message += '    spawnfile: `' + worker.spawnfile + '`\\n'\n\n    message += '    stdout: `' + worker.stdout + '`\\n'\n    message += '    stderr: `' + worker.stderr + '`\\n'\n\n    onError(new Error(message));\n  });\n\n  this.processing = Object.create(null); // queue with tasks currently in progress\n  this.tracking = Object.create(null); // queue with tasks being monitored for cleanup status\n  this.terminating = false;\n  this.terminated = false;\n  this.cleaning = false;\n  this.terminationHandler = null;\n  this.lastId = 0;\n}\n\n/**\n * Get a list with methods available on the worker.\n * @return {Promise.<String[], Error>} methods\n */\nWorkerHandler.prototype.methods = function () {\n  return this.exec('methods');\n};\n\n/**\n * Execute a method with given parameters on the worker\n * @param {String} method\n * @param {Array} [params]\n * @param {{resolve: Function, reject: Function}} [resolver]\n * @param {import('./types.js').ExecOptions}  [options]\n * @return {Promise.<*, Error>} result\n */\nWorkerHandler.prototype.exec = function(method, params, resolver, options) {\n  if (!resolver) {\n    resolver = Promise.defer();\n  }\n\n  // generate a unique id for the task\n  var id = ++this.lastId;\n\n  // register a new task as being in progress\n  this.processing[id] = {\n    id: id,\n    resolver: resolver,\n    options: options\n  };\n\n  // build a JSON-RPC request\n  var request = {\n    message: {\n      id: id,\n      method: method,\n      params: params\n    },\n    transfer: options && options.transfer\n  };\n\n  if (this.terminated) {\n    resolver.reject(new Error('Worker is terminated'));\n  } else if (this.worker.ready) {\n    // send the request to the worker\n    this.worker.send(request.message, request.transfer);\n  } else {\n    this.requestQueue.push(request);\n  }\n\n  // on cancellation, force the worker to terminate\n  var me = this;\n  return resolver.promise.catch(function (error) {\n    if (error instanceof Promise.CancellationError || error instanceof Promise.TimeoutError) {\n      me.tracking[id] = {\n        id,\n        resolver: Promise.defer(),\n        options: options,\n        error,\n      };\n      \n      // remove this task from the queue. It is already rejected (hence this\n      // catch event), and else it will be rejected again when terminating\n      delete me.processing[id];\n\n      me.tracking[id].resolver.promise = me.tracking[id].resolver.promise.catch(function(err) {\n        delete me.tracking[id];\n\n        // if we find the error is an instance of WrappedTimeoutError we know the error should not cause termination\n        // as the response from the worker did not contain an error. We still wish to throw the original timeout error\n        // to the caller.\n        if (err instanceof WrappedTimeoutError) {\n          throw err.error;\n        }\n\n        var promise = me.terminateAndNotify(true)\n          .then(function() { \n            throw err;\n          }, function(err) {\n            throw err;\n          });\n\n        return promise;\n      });\n \n      me.worker.send({\n        id,\n        method: CLEANUP_METHOD_ID \n      });\n      \n      \n      /**\n        * Sets a timeout to reject the cleanup operation if the message sent to the worker\n        * does not receive a response. see worker.tryCleanup for worker cleanup operations.\n        * Here we use the workerTerminateTimeout as the worker will be terminated if the timeout does invoke.\n        * \n        * We need this timeout in either case of a Timeout or Cancellation Error as if\n        * the worker does not send a message we still need to give a window of time for a response.\n        * \n        * The workerTermniateTimeout is used here if this promise is rejected the worker cleanup\n        * operations will occure.\n      */\n      me.tracking[id].timeoutId = setTimeout(function() {\n          me.tracking[id].resolver.reject(error);\n      }, me.workerTerminateTimeout);\n\n      return me.tracking[id].resolver.promise;\n    } else {\n      throw error;\n    }\n  })\n};\n\n/**\n * Test whether the worker is processing any tasks or cleaning up before termination.\n * @return {boolean} Returns true if the worker is busy\n */\nWorkerHandler.prototype.busy = function () {\n  return this.cleaning || Object.keys(this.processing).length > 0;\n};\n\n/**\n * Terminate the worker.\n * @param {boolean} [force=false]   If false (default), the worker is terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the worker will be\n *                                  terminated immediately.\n * @param {function} [callback=null] If provided, will be called when process terminates.\n */\nWorkerHandler.prototype.terminate = function (force, callback) {\n  var me = this;\n  if (force) {\n    // cancel all tasks in progress\n    for (var id in this.processing) {\n      if (this.processing[id] !== undefined) {\n        this.processing[id].resolver.reject(new Error('Worker terminated'));\n      }\n    }\n\n    this.processing = Object.create(null);\n  }\n\n  // If we are terminating, cancel all tracked task for cleanup\n  for (var task of Object.values(me.tracking)) {\n    clearTimeout(task.timeoutId);\n    task.resolver.reject(new Error('Worker Terminating'));\n  }\n\n  me.tracking = Object.create(null);\n\n  if (typeof callback === 'function') {\n    this.terminationHandler = callback;\n  }\n  if (!this.busy()) {\n    // all tasks are finished. kill the worker\n    var cleanup = function(err) {\n      me.terminated = true;\n      me.cleaning = false;\n\n      if (me.worker != null && me.worker.removeAllListeners) {\n        // removeAllListeners is only available for child_process\n        me.worker.removeAllListeners('message');\n      }\n      me.worker = null;\n      me.terminating = false;\n      if (me.terminationHandler) {\n        me.terminationHandler(err, me);\n      } else if (err) {\n        throw err;\n      }\n    }\n\n    if (this.worker) {\n      if (typeof this.worker.kill === 'function') {\n        if (this.worker.killed) {\n          cleanup(new Error('worker already killed!'));\n          return;\n        }\n\n        // child process and worker threads\n        var cleanExitTimeout = setTimeout(function() {\n          if (me.worker) {\n            me.worker.kill();\n          }\n        }, this.workerTerminateTimeout);\n\n        this.worker.once('exit', function() {\n          clearTimeout(cleanExitTimeout);\n          if (me.worker) {\n            me.worker.killed = true;\n          }\n          cleanup();\n        });\n\n        if (this.worker.ready) {\n          this.worker.send(TERMINATE_METHOD_ID);\n        } else {\n          this.requestQueue.push({ message: TERMINATE_METHOD_ID });\n        }\n\n        // mark that the worker is cleaning up resources\n        // to prevent new tasks from being executed\n        this.cleaning = true;\n        return;\n      }\n      else if (typeof this.worker.terminate === 'function') {\n        this.worker.terminate(); // web worker\n        this.worker.killed = true;\n      }\n      else {\n        throw new Error('Failed to terminate worker');\n      }\n    }\n    cleanup();\n  }\n  else {\n    // we can't terminate immediately, there are still tasks being executed\n    this.terminating = true;\n  }\n};\n\n/**\n * Terminate the worker, returning a Promise that resolves when the termination has been done.\n * @param {boolean} [force=false]   If false (default), the worker is terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the worker will be\n *                                  terminated immediately.\n * @param {number} [timeout]        If provided and non-zero, worker termination promise will be rejected\n *                                  after timeout if worker process has not been terminated.\n * @return {Promise.<WorkerHandler, Error>}\n */\nWorkerHandler.prototype.terminateAndNotify = function (force, timeout) {\n  var resolver = Promise.defer();\n  if (timeout) {\n    resolver.promise.timeout(timeout);\n  }\n  this.terminate(force, function(err, worker) {\n    if (err) {\n      resolver.reject(err);\n    } else {\n      resolver.resolve(worker);\n    }\n  });\n  return resolver.promise;\n};\n\n/**\n* Wrapper error type to denote that a TimeoutError has already been proceesed\n* and we should skip cleanup operations\n* @param {Promise.TimeoutError} timeoutError\n*/\nfunction WrappedTimeoutError(timeoutError) {\n  this.error = timeoutError;\n  this.stack = (new Error()).stack;\n}\n\nmodule.exports = WorkerHandler;\nmodule.exports._tryRequireWorkerThreads = tryRequireWorkerThreads;\nmodule.exports._setupProcessWorker = setupProcessWorker;\nmodule.exports._setupBrowserWorker = setupBrowserWorker;\nmodule.exports._setupWorkerThreadWorker = setupWorkerThreadWorker;\nmodule.exports.ensureWorkerThreads = ensureWorkerThreads;\n", "/**\n * embeddedWorker.js contains an embedded version of worker.js.\n * This file is automatically generated,\n * changes made in this file will be overwritten.\n */\nmodule.exports = \"!function(e,n){\\\"object\\\"==typeof exports&&\\\"undefined\\\"!=typeof module?module.exports=n():\\\"function\\\"==typeof define&&define.amd?define(n):(e=\\\"undefined\\\"!=typeof globalThis?globalThis:e||self).worker=n()}(this,(function(){\\\"use strict\\\";function e(n){return e=\\\"function\\\"==typeof Symbol&&\\\"symbol\\\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\\\"function\\\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\\\"symbol\\\":typeof e},e(n)}function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,\\\"default\\\")?e.default:e}var t={};var r=function(e,n){this.message=e,this.transfer=n},o={};function i(e,n){var t=this;if(!(this instanceof i))throw new SyntaxError(\\\"Constructor must be called with the new operator\\\");if(\\\"function\\\"!=typeof e)throw new SyntaxError(\\\"Function parameter handler(resolve, reject) missing\\\");var r=[],o=[];this.resolved=!1,this.rejected=!1,this.pending=!0,this[Symbol.toStringTag]=\\\"Promise\\\";var a=function(e,n){r.push(e),o.push(n)};this.then=function(e,n){return new i((function(t,r){var o=e?u(e,t,r):t,i=n?u(n,t,r):r;a(o,i)}),t)};var f=function(e){return t.resolved=!0,t.rejected=!1,t.pending=!1,r.forEach((function(n){n(e)})),a=function(n,t){n(e)},f=d=function(){},t},d=function(e){return t.resolved=!1,t.rejected=!0,t.pending=!1,o.forEach((function(n){n(e)})),a=function(n,t){t(e)},f=d=function(){},t};this.cancel=function(){return n?n.cancel():d(new s),t},this.timeout=function(e){if(n)n.timeout(e);else{var r=setTimeout((function(){d(new c(\\\"Promise timed out after \\\"+e+\\\" ms\\\"))}),e);t.always((function(){clearTimeout(r)}))}return t},e((function(e){f(e)}),(function(e){d(e)}))}function u(e,n,t){return function(r){try{var o=e(r);o&&\\\"function\\\"==typeof o.then&&\\\"function\\\"==typeof o.catch?o.then(n,t):n(o)}catch(e){t(e)}}}function s(e){this.message=e||\\\"promise cancelled\\\",this.stack=(new Error).stack}function c(e){this.message=e||\\\"timeout exceeded\\\",this.stack=(new Error).stack}return i.prototype.catch=function(e){return this.then(null,e)},i.prototype.always=function(e){return this.then(e,e)},i.prototype.finally=function(e){var n=this,t=function(){return new i((function(e){return e()})).then(e).then((function(){return n}))};return this.then(t,t)},i.all=function(e){return new i((function(n,t){var r=e.length,o=[];r?e.forEach((function(e,i){e.then((function(e){o[i]=e,0==--r&&n(o)}),(function(e){r=0,t(e)}))})):n(o)}))},i.defer=function(){var e={};return e.promise=new i((function(n,t){e.resolve=n,e.reject=t})),e},s.prototype=new Error,s.prototype.constructor=Error,s.prototype.name=\\\"CancellationError\\\",i.CancellationError=s,c.prototype=new Error,c.prototype.constructor=Error,c.prototype.name=\\\"TimeoutError\\\",i.TimeoutError=c,o.Promise=i,function(n){var t=r,i=o.Promise,u=\\\"__workerpool-cleanup__\\\",s={exit:function(){}},c={addAbortListener:function(e){s.abortListeners.push(e)},emit:s.emit};if(\\\"undefined\\\"!=typeof self&&\\\"function\\\"==typeof postMessage&&\\\"function\\\"==typeof addEventListener)s.on=function(e,n){addEventListener(e,(function(e){n(e.data)}))},s.send=function(e,n){n?postMessage(e,n):postMessage(e)};else{if(\\\"undefined\\\"==typeof process)throw new Error(\\\"Script must be executed as a worker\\\");var a;try{a=require(\\\"worker_threads\\\")}catch(n){if(\\\"object\\\"!==e(n)||null===n||\\\"MODULE_NOT_FOUND\\\"!==n.code)throw n}if(a&&null!==a.parentPort){var f=a.parentPort;s.send=f.postMessage.bind(f),s.on=f.on.bind(f),s.exit=process.exit.bind(process)}else s.on=process.on.bind(process),s.send=function(e){process.send(e)},s.on(\\\"disconnect\\\",(function(){process.exit(1)})),s.exit=process.exit.bind(process)}function d(e){return Object.getOwnPropertyNames(e).reduce((function(n,t){return Object.defineProperty(n,t,{value:e[t],enumerable:!0})}),{})}function l(e){return e&&\\\"function\\\"==typeof e.then&&\\\"function\\\"==typeof e.catch}s.methods={},s.methods.run=function(e,n){var t=new Function(\\\"return (\\\"+e+\\\").apply(this, arguments);\\\");return t.worker=c,t.apply(t,n)},s.methods.methods=function(){return Object.keys(s.methods)},s.terminationHandler=void 0,s.abortListenerTimeout=1e3,s.abortListeners=[],s.terminateAndExit=function(e){var n=function(){s.exit(e)};if(!s.terminationHandler)return n();var t=s.terminationHandler(e);return l(t)?(t.then(n,n),t):(n(),new i((function(e,n){n(new Error(\\\"Worker terminating\\\"))})))},s.cleanup=function(e){if(!s.abortListeners.length)return s.send({id:e,method:u,error:d(new Error(\\\"Worker terminating\\\"))}),new i((function(e){e()}));var n,t=s.abortListeners.map((function(e){return e()})),r=new i((function(e,t){n=setTimeout((function(){t(new Error(\\\"Timeout occured waiting for abort handler, killing worker\\\"))}),s.abortListenerTimeout)})),o=i.all(t).then((function(){clearTimeout(n),s.abortListeners.length||(s.abortListeners=[])}),(function(){clearTimeout(n),s.exit()}));return new i((function(e,n){o.then(e,n),r.then(e,n)})).then((function(){s.send({id:e,method:u,error:null})}),(function(n){s.send({id:e,method:u,error:n?d(n):null})}))};var p=null;s.on(\\\"message\\\",(function(e){if(\\\"__workerpool-terminate__\\\"===e)return s.terminateAndExit(0);if(e.method===u)return s.cleanup(e.id);try{var n=s.methods[e.method];if(!n)throw new Error('Unknown method \\\"'+e.method+'\\\"');p=e.id;var r=n.apply(n,e.params);l(r)?r.then((function(n){n instanceof t?s.send({id:e.id,result:n.message,error:null},n.transfer):s.send({id:e.id,result:n,error:null}),p=null})).catch((function(n){s.send({id:e.id,result:null,error:d(n)}),p=null})):(r instanceof t?s.send({id:e.id,result:r.message,error:null},r.transfer):s.send({id:e.id,result:r,error:null}),p=null)}catch(n){s.send({id:e.id,result:null,error:d(n)})}})),s.register=function(e,n){if(e)for(var t in e)e.hasOwnProperty(t)&&(s.methods[t]=e[t],s.methods[t].worker=c);n&&(s.terminationHandler=n.onTerminate,s.abortListenerTimeout=n.abortListenerTimeout||1e3),s.send(\\\"ready\\\")},s.emit=function(e){if(p){if(e instanceof t)return void s.send({id:p,isEvent:!0,payload:e.message},e.transfer);s.send({id:p,isEvent:!0,payload:e})}},n.add=s.register,n.emit=s.emit}(t),n(t)}));\\n//# sourceMappingURL=worker.min.js.map\\n\";\n", "var {Promise} = require('./Promise');\nvar WorkerHandler = require('./WorkerHandler');\nvar environment = require('./environment');\nvar DebugPortAllocator = require('./debug-port-allocator');\nvar DEBUG_PORT_ALLOCATOR = new DebugPortAllocator();\n/**\n * A pool to manage workers, which can be created using the function workerpool.pool.\n *\n * @param {String} [script]   Optional worker script\n * @param {import('./types.js').WorkerPoolOptions} [options]  See docs\n * @constructor\n */\nfunction Pool(script, options) {\n  if (typeof script === 'string') {\n    /** @readonly */\n    this.script = script || null;\n  }\n  else {\n    this.script = null;\n    options = script;\n  }\n\n  /** @private */\n  this.workers = [];  // queue with all workers\n  /** @private */\n  this.tasks = [];    // queue with tasks awaiting execution\n\n  options = options || {};\n\n  /** @readonly */\n  this.forkArgs = Object.freeze(options.forkArgs || []);\n  /** @readonly */\n  this.forkOpts = Object.freeze(options.forkOpts || {});\n  /** @readonly */\n  this.workerOpts = Object.freeze(options.workerOpts || {});\n  /** @readonly */\n  this.workerThreadOpts = Object.freeze(options.workerThreadOpts || {})\n  /** @private */\n  this.debugPortStart = (options.debugPortStart || 43210);\n  /** @readonly @deprecated */\n  this.nodeWorker = options.nodeWorker;\n  /** @readonly\n   * @type {'auto' | 'web' | 'process' | 'thread'}\n   */\n  this.workerType = options.workerType || options.nodeWorker || 'auto'\n  /** @readonly */\n  this.maxQueueSize = options.maxQueueSize || Infinity;\n  /** @readonly */\n  this.workerTerminateTimeout = options.workerTerminateTimeout || 1000;\n\n  /** @readonly */\n  this.onCreateWorker = options.onCreateWorker || (() => null);\n  /** @readonly */\n  this.onTerminateWorker = options.onTerminateWorker || (() => null);\n\n  /** @readonly */\n  this.emitStdStreams = options.emitStdStreams || false\n\n  // configuration\n  if (options && 'maxWorkers' in options) {\n    validateMaxWorkers(options.maxWorkers);\n    /** @readonly */\n    this.maxWorkers = options.maxWorkers;\n  }\n  else {\n    this.maxWorkers = Math.max((environment.cpus || 4) - 1, 1);\n  }\n\n  if (options && 'minWorkers' in options) {\n    if(options.minWorkers === 'max') {\n      /** @readonly */\n      this.minWorkers = this.maxWorkers;\n    } else {\n      validateMinWorkers(options.minWorkers);\n      this.minWorkers = options.minWorkers;\n      this.maxWorkers = Math.max(this.minWorkers, this.maxWorkers);     // in case minWorkers is higher than maxWorkers\n    }\n    this._ensureMinWorkers();\n  }\n\n  /** @private */\n  this._boundNext = this._next.bind(this);\n\n\n  if (this.workerType === 'thread') {\n    WorkerHandler.ensureWorkerThreads();\n  }\n}\n\n\n/**\n * Execute a function on a worker.\n *\n * Example usage:\n *\n *   var pool = new Pool()\n *\n *   // call a function available on the worker\n *   pool.exec('fibonacci', [6])\n *\n *   // offload a function\n *   function add(a, b) {\n *     return a + b\n *   };\n *   pool.exec(add, [2, 4])\n *       .then(function (result) {\n *         console.log(result); // outputs 6\n *       })\n *       .catch(function(error) {\n *         console.log(error);\n *       });\n * @template { (...args: any[]) => any } T\n * @param {String | T} method  Function name or function.\n *                                    If `method` is a string, the corresponding\n *                                    method on the worker will be executed\n *                                    If `method` is a Function, the function\n *                                    will be stringified and executed via the\n *                                    workers built-in function `run(fn, args)`.\n * @param {Parameters<T> | null} [params]  Function arguments applied when calling the function\n * @param {import('./types.js').ExecOptions} [options]  Options\n * @return {Promise<ReturnType<T>>}\n */\nPool.prototype.exec = function (method, params, options) {\n  // validate type of arguments\n  if (params && !Array.isArray(params)) {\n    throw new TypeError('Array expected as argument \"params\"');\n  }\n\n  if (typeof method === 'string') {\n    var resolver = Promise.defer();\n\n    if (this.tasks.length >= this.maxQueueSize) {\n      throw new Error('Max queue size of ' + this.maxQueueSize + ' reached');\n    }\n\n    // add a new task to the queue\n    var tasks = this.tasks;\n    var task = {\n      method:  method,\n      params:  params,\n      resolver: resolver,\n      timeout: null,\n      options: options\n    };\n    tasks.push(task);\n\n    // replace the timeout method of the Promise with our own,\n    // which starts the timer as soon as the task is actually started\n    var originalTimeout = resolver.promise.timeout;\n    resolver.promise.timeout = function timeout (delay) {\n      if (tasks.indexOf(task) !== -1) {\n        // task is still queued -> start the timer later on\n        task.timeout = delay;\n        return resolver.promise;\n      }\n      else {\n        // task is already being executed -> start timer immediately\n        return originalTimeout.call(resolver.promise, delay);\n      }\n    };\n\n    // trigger task execution\n    this._next();\n\n    return resolver.promise;\n  }\n  else if (typeof method === 'function') {\n    // send stringified function and function arguments to worker\n    return this.exec('run', [String(method), params], options);\n  }\n  else {\n    throw new TypeError('Function or string expected as argument \"method\"');\n  }\n};\n\n/**\n * Create a proxy for current worker. Returns an object containing all\n * methods available on the worker. All methods return promises resolving the methods result.\n * @template { { [k: string]: (...args: any[]) => any } } T\n * @return {Promise<import('./types.js').Proxy<T>, Error>} Returns a promise which resolves with a proxy object\n */\nPool.prototype.proxy = function () {\n  if (arguments.length > 0) {\n    throw new Error('No arguments expected');\n  }\n\n  var pool = this;\n  return this.exec('methods')\n      .then(function (methods) {\n        var proxy = {};\n\n        methods.forEach(function (method) {\n          proxy[method] = function () {\n            return pool.exec(method, Array.prototype.slice.call(arguments));\n          }\n        });\n\n        return proxy;\n      });\n};\n\n/**\n * Creates new array with the results of calling a provided callback function\n * on every element in this array.\n * @param {Array} array\n * @param {function} callback  Function taking two arguments:\n *                             `callback(currentValue, index)`\n * @return {Promise.<Array>} Returns a promise which resolves  with an Array\n *                           containing the results of the callback function\n *                           executed for each of the array elements.\n */\n/* TODO: implement map\nPool.prototype.map = function (array, callback) {\n};\n*/\n\n/**\n * Grab the first task from the queue, find a free worker, and assign the\n * worker to the task.\n * @private\n */\nPool.prototype._next = function () {\n  if (this.tasks.length > 0) {\n    // there are tasks in the queue\n\n    // find an available worker\n    var worker = this._getWorker();\n    if (worker) {\n      // get the first task from the queue\n      var me = this;\n      var task = this.tasks.shift();\n\n      // check if the task is still pending (and not cancelled -> promise rejected)\n      if (task.resolver.promise.pending) {\n        // send the request to the worker\n        var promise = worker.exec(task.method, task.params, task.resolver, task.options)\n          .then(me._boundNext)\n          .catch(function () {\n            // if the worker crashed and terminated, remove it from the pool\n            if (worker.terminated) {\n              return me._removeWorker(worker);\n            }\n          }).then(function() {\n            me._next(); // trigger next task in the queue\n          });\n\n        // start queued timer now\n        if (typeof task.timeout === 'number') {\n          promise.timeout(task.timeout);\n        }\n      } else {\n        // The task taken was already complete (either rejected or resolved), so just trigger next task in the queue\n        me._next();\n      }\n    }\n  }\n};\n\n/**\n * Get an available worker. If no worker is available and the maximum number\n * of workers isn't yet reached, a new worker will be created and returned.\n * If no worker is available and the maximum number of workers is reached,\n * null will be returned.\n *\n * @return {WorkerHandler | null} worker\n * @private\n */\nPool.prototype._getWorker = function() {\n  // find a non-busy worker\n  var workers = this.workers;\n  for (var i = 0; i < workers.length; i++) {\n    var worker = workers[i];\n    if (worker.busy() === false) {\n      return worker;\n    }\n  }\n\n  if (workers.length < this.maxWorkers) {\n    // create a new worker\n    worker = this._createWorkerHandler();\n    workers.push(worker);\n    return worker;\n  }\n\n  return null;\n};\n\n/**\n * Remove a worker from the pool.\n * Attempts to terminate worker if not already terminated, and ensures the minimum\n * pool size is met.\n * @param {WorkerHandler} worker\n * @return {Promise<WorkerHandler>}\n * @private\n */\nPool.prototype._removeWorker = function(worker) {\n  var me = this;\n\n  DEBUG_PORT_ALLOCATOR.releasePort(worker.debugPort);\n  // _removeWorker will call this, but we need it to be removed synchronously\n  this._removeWorkerFromList(worker);\n  // If minWorkers set, spin up new workers to replace the crashed ones\n  this._ensureMinWorkers();\n  // terminate the worker (if not already terminated)\n  return new Promise(function(resolve, reject) {\n    worker.terminate(false, function(err) {\n      me.onTerminateWorker({\n        forkArgs: worker.forkArgs,\n        forkOpts: worker.forkOpts,\n        workerThreadOpts: worker.workerThreadOpts,\n        script: worker.script\n      });\n      if (err) {\n        reject(err);\n      } else {\n        resolve(worker);\n      }\n    });\n  });\n};\n\n/**\n * Remove a worker from the pool list.\n * @param {WorkerHandler} worker\n * @private\n */\nPool.prototype._removeWorkerFromList = function(worker) {\n  // remove from the list with workers\n  var index = this.workers.indexOf(worker);\n  if (index !== -1) {\n    this.workers.splice(index, 1);\n  }\n};\n\n/**\n * Close all active workers. Tasks currently being executed will be finished first.\n * @param {boolean} [force=false]   If false (default), the workers are terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the workers will be\n *                                  terminated immediately.\n * @param {number} [timeout]        If provided and non-zero, worker termination promise will be rejected\n *                                  after timeout if worker process has not been terminated.\n * @return {Promise.<void, Error>}\n */\nPool.prototype.terminate = function (force, timeout) {\n  var me = this;\n\n  // cancel any pending tasks\n  this.tasks.forEach(function (task) {\n    task.resolver.reject(new Error('Pool terminated'));\n  });\n  this.tasks.length = 0;\n\n  var f = function (worker) {\n    DEBUG_PORT_ALLOCATOR.releasePort(worker.debugPort);\n    this._removeWorkerFromList(worker);\n  };\n  var removeWorker = f.bind(this);\n\n  var promises = [];\n  var workers = this.workers.slice();\n  workers.forEach(function (worker) {\n    var termPromise = worker.terminateAndNotify(force, timeout)\n      .then(removeWorker)\n      .always(function() {\n        me.onTerminateWorker({\n          forkArgs: worker.forkArgs,\n          forkOpts: worker.forkOpts,\n          workerThreadOpts: worker.workerThreadOpts,\n          script: worker.script\n        });\n      });\n    promises.push(termPromise);\n  });\n  return Promise.all(promises);\n};\n\n/**\n * Retrieve statistics on tasks and workers.\n * @return {{totalWorkers: number, busyWorkers: number, idleWorkers: number, pendingTasks: number, activeTasks: number}} Returns an object with statistics\n */\nPool.prototype.stats = function () {\n  var totalWorkers = this.workers.length;\n  var busyWorkers = this.workers.filter(function (worker) {\n    return worker.busy();\n  }).length;\n\n  return {\n    totalWorkers:  totalWorkers,\n    busyWorkers:   busyWorkers,\n    idleWorkers:   totalWorkers - busyWorkers,\n\n    pendingTasks:  this.tasks.length,\n    activeTasks:   busyWorkers\n  };\n};\n\n/**\n * Ensures that a minimum of minWorkers is up and running\n * @private\n */\nPool.prototype._ensureMinWorkers = function() {\n  if (this.minWorkers) {\n    for(var i = this.workers.length; i < this.minWorkers; i++) {\n      this.workers.push(this._createWorkerHandler());\n    }\n  }\n};\n\n/**\n * Helper function to create a new WorkerHandler and pass all options.\n * @return {WorkerHandler}\n * @private\n */\nPool.prototype._createWorkerHandler = function () {\n  const overriddenParams = this.onCreateWorker({\n    forkArgs: this.forkArgs,\n    forkOpts: this.forkOpts,\n    workerOpts: this.workerOpts,\n    workerThreadOpts: this.workerThreadOpts,\n    script: this.script\n  }) || {};\n\n  return new WorkerHandler(overriddenParams.script || this.script, {\n    forkArgs: overriddenParams.forkArgs || this.forkArgs,\n    forkOpts: overriddenParams.forkOpts || this.forkOpts,\n    workerOpts: overriddenParams.workerOpts || this.workerOpts,\n    workerThreadOpts: overriddenParams.workerThreadOpts || this.workerThreadOpts,\n    debugPort: DEBUG_PORT_ALLOCATOR.nextAvailableStartingAt(this.debugPortStart),\n    workerType: this.workerType,\n    workerTerminateTimeout: this.workerTerminateTimeout,\n    emitStdStreams: this.emitStdStreams,\n  });\n}\n\n/**\n * Ensure that the maxWorkers option is an integer >= 1\n * @param {*} maxWorkers\n * @returns {boolean} returns true maxWorkers has a valid value\n */\nfunction validateMaxWorkers(maxWorkers) {\n  if (!isNumber(maxWorkers) || !isInteger(maxWorkers) || maxWorkers < 1) {\n    throw new TypeError('Option maxWorkers must be an integer number >= 1');\n  }\n}\n\n/**\n * Ensure that the minWorkers option is an integer >= 0\n * @param {*} minWorkers\n * @returns {boolean} returns true when minWorkers has a valid value\n */\nfunction validateMinWorkers(minWorkers) {\n  if (!isNumber(minWorkers) || !isInteger(minWorkers) || minWorkers < 0) {\n    throw new TypeError('Option minWorkers must be an integer number >= 0');\n  }\n}\n\n/**\n * Test whether a variable is a number\n * @param {*} value\n * @returns {boolean} returns true when value is a number\n */\nfunction isNumber(value) {\n  return typeof value === 'number';\n}\n\n/**\n * Test whether a number is an integer\n * @param {number} value\n * @returns {boolean} Returns true if value is an integer\n */\nfunction isInteger(value) {\n  return Math.round(value) == value;\n}\n\nmodule.exports = Pool;\n", "'use strict';\n\nvar MAX_PORTS = 65535;\nmodule.exports = DebugPortAllocator;\nfunction DebugPortAllocator() {\n  this.ports = Object.create(null);\n  this.length = 0;\n}\n\nDebugPortAllocator.prototype.nextAvailableStartingAt = function(starting) {\n  while (this.ports[starting] === true) {\n    starting++;\n  }\n\n  if (starting >= MAX_PORTS) {\n    throw new Error('WorkerPool debug port limit reached: ' + starting + '>= ' + MAX_PORTS );\n  }\n\n  this.ports[starting] = true;\n  this.length++;\n  return starting;\n};\n\nDebugPortAllocator.prototype.releasePort = function(port) {\n  delete this.ports[port];\n  this.length--;\n};\n\n", "/**\n * The helper class for transferring data from the worker to the main thread.\n *\n * @param {Object} message The object to deliver to the main thread.\n * @param {Object[]} transfer An array of transferable Objects to transfer ownership of.\n */\nfunction Transfer(message, transfer) {\n  this.message = message;\n  this.transfer = transfer;\n}\n\nmodule.exports = Transfer;\n", "/**\n * worker must be started as a child process or a web worker.\n * It listens for RPC messages from the parent process.\n */\n\nvar Transfer = require('./transfer');\n\n/**\n * worker must handle async cleanup handlers. Use custom Promise implementation. \n*/\nvar Promise = require('./Promise').Promise;\n/**\n * Special message sent by parent which causes the worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\n/**\n * Special message by parent which causes a child process worker to perform cleaup\n * steps before determining if the child process worker should be terminated.\n*/\nvar CLEANUP_METHOD_ID = '__workerpool-cleanup__';\n// var nodeOSPlatform = require('./environment').nodeOSPlatform;\n\n\nvar TIMEOUT_DEFAULT = 1_000;\n\n// create a worker API for sending and receiving messages which works both on\n// node.js and in the browser\nvar worker = {\n  exit: function() {}\n};\n\n// api for in worker communication with parent process\n// works in both node.js and the browser\nvar publicWorker = {\n  /**\n   * Registers listeners which will trigger when a task is timed out or cancled. If all listeners resolve, the worker executing the given task will not be terminated.\n   * *Note*: If there is a blocking operation within a listener, the worker will be terminated.\n   * @param {() => Promise<void>} listener\n  */\n  addAbortListener: function(listener) {\n    worker.abortListeners.push(listener);\n  },\n\n  /**\n    * Emit an event from the worker thread to the main thread.\n    * @param {any} payload\n  */\n  emit: worker.emit\n};\n\nif (typeof self !== 'undefined' && typeof postMessage === 'function' && typeof addEventListener === 'function') {\n  // worker in the browser\n  worker.on = function (event, callback) {\n    addEventListener(event, function (message) {\n      callback(message.data);\n    })\n  };\n  worker.send = function (message, transfer) {\n     transfer ? postMessage(message, transfer) : postMessage (message);\n  };\n}\nelse if (typeof process !== 'undefined') {\n  // node.js\n\n  var WorkerThreads;\n  try {\n    WorkerThreads = require('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads, fallback to sub-process based workers\n    } else {\n      throw error;\n    }\n  }\n\n  if (WorkerThreads &&\n    /* if there is a parentPort, we are in a WorkerThread */\n    WorkerThreads.parentPort !== null) {\n    var parentPort  = WorkerThreads.parentPort;\n    worker.send = parentPort.postMessage.bind(parentPort);\n    worker.on = parentPort.on.bind(parentPort);\n    worker.exit = process.exit.bind(process);\n  } else {\n    worker.on = process.on.bind(process);\n    // ignore transfer argument since it is not supported by process\n    worker.send = function (message) {\n      process.send(message);\n    };\n    // register disconnect handler only for subprocess worker to exit when parent is killed unexpectedly\n    worker.on('disconnect', function () {\n      process.exit(1);\n    });\n    worker.exit = process.exit.bind(process);\n  }\n}\nelse {\n  throw new Error('Script must be executed as a worker');\n}\n\nfunction convertError(error) {\n  return Object.getOwnPropertyNames(error).reduce(function(product, name) {\n    return Object.defineProperty(product, name, {\n\tvalue: error[name],\n\tenumerable: true\n    });\n  }, {});\n}\n\n/**\n * Test whether a value is a Promise via duck typing.\n * @param {*} value\n * @returns {boolean} Returns true when given value is an object\n *                    having functions `then` and `catch`.\n */\nfunction isPromise(value) {\n  return value && (typeof value.then === 'function') && (typeof value.catch === 'function');\n}\n\n// functions available externally\nworker.methods = {};\n\n/**\n * Execute a function with provided arguments\n * @param {String} fn     Stringified function\n * @param {Array} [args]  Function arguments\n * @returns {*}\n */\nworker.methods.run = function run(fn, args) {\n  var f = new Function('return (' + fn + ').apply(this, arguments);');\n  f.worker = publicWorker;\n  return f.apply(f, args);\n};\n\n/**\n * Get a list with methods available on this worker\n * @return {String[]} methods\n */\nworker.methods.methods = function methods() {\n  return Object.keys(worker.methods);\n};\n\n/**\n * Custom handler for when the worker is terminated.\n */\nworker.terminationHandler = undefined;\n\nworker.abortListenerTimeout = TIMEOUT_DEFAULT;\n\n/**\n * Abort handlers for resolving errors which may cause a timeout or cancellation\n * to occur from a worker context\n */\nworker.abortListeners = [];\n\n/**\n * Cleanup and exit the worker.\n * @param {Number} code \n * @returns {Promise<void>}\n */\nworker.terminateAndExit = function(code) {\n  var _exit = function() {\n    worker.exit(code);\n  }\n\n  if(!worker.terminationHandler) {\n    return _exit();\n  }\n  \n  var result = worker.terminationHandler(code);\n  if (isPromise(result)) {\n    result.then(_exit, _exit);\n\n    return result;\n  } else {\n    _exit();\n    return new Promise(function (_resolve, reject) {\n      reject(new Error(\"Worker terminating\"));\n    });\n  }\n}\n\n\n\n/**\n  * Called within the worker message handler to run abort handlers if registered to perform cleanup operations.\n  * @param {Integer} [requestId] id of task which is currently executing in the worker\n  * @return {Promise<void>}\n*/\nworker.cleanup = function(requestId) {\n\n  if (!worker.abortListeners.length) {\n    worker.send({\n      id: requestId,\n      method: CLEANUP_METHOD_ID,\n      error: convertError(new Error('Worker terminating')),\n    });\n\n    // If there are no handlers registered, reject the promise with an error as we want the handler to be notified\n    // that cleanup should begin and the handler should be GCed.\n    return new Promise(function(resolve) { resolve(); });\n  }\n  \n\n  var _exit = function() {\n    worker.exit();\n  }\n\n  var _abort = function() {\n    if (!worker.abortListeners.length) {\n      worker.abortListeners = [];\n    }\n  }\n\n  const promises = worker.abortListeners.map(listener => listener());\n  let timerId;\n  const timeoutPromise = new Promise((_resolve, reject) => {\n    timerId = setTimeout(function () { \n      reject(new Error('Timeout occured waiting for abort handler, killing worker'));\n    }, worker.abortListenerTimeout);\n  });\n\n  // Once a promise settles we need to clear the timeout to prevet fulfulling the promise twice \n  const settlePromise = Promise.all(promises).then(function() {\n    clearTimeout(timerId);\n    _abort();\n  }, function() {\n    clearTimeout(timerId);\n    _exit();\n  });\n\n  // Returns a promise which will result in one of the following cases\n  // - Resolve once all handlers resolve\n  // - Reject if one or more handlers exceed the 'abortListenerTimeout' interval\n  // - Reject if one or more handlers reject\n  // Upon one of the above cases a message will be sent to the handler with the result of the handler execution\n  // which will either kill the worker if the result contains an error, or keep it in the pool if the result\n  // does not contain an error.\n  return new Promise(function(resolve, reject) {\n    settlePromise.then(resolve, reject);\n    timeoutPromise.then(resolve, reject);\n  }).then(function() {\n    worker.send({\n      id: requestId,\n      method: CLEANUP_METHOD_ID,\n      error: null,\n    });\n  }, function(err) {\n    worker.send({\n      id: requestId,\n      method: CLEANUP_METHOD_ID,\n      error: err ? convertError(err) : null,\n    });\n  });\n}\n\nvar currentRequestId = null;\n\nworker.on('message', function (request) {\n  if (request === TERMINATE_METHOD_ID) {\n    return worker.terminateAndExit(0);\n  }\n\n  if (request.method === CLEANUP_METHOD_ID) {\n    return worker.cleanup(request.id);\n  }\n\n  try {\n    var method = worker.methods[request.method];\n\n    if (method) {\n      currentRequestId = request.id;\n      \n      // execute the function\n      var result = method.apply(method, request.params);\n\n      if (isPromise(result)) {\n        // promise returned, resolve this and then return\n        result\n            .then(function (result) {\n              if (result instanceof Transfer) {\n                worker.send({\n                  id: request.id,\n                  result: result.message,\n                  error: null\n                }, result.transfer);\n              } else {\n                worker.send({\n                  id: request.id,\n                  result: result,\n                  error: null\n                });\n              }\n              currentRequestId = null;\n            })\n            .catch(function (err) {\n              worker.send({\n                id: request.id,\n                result: null,\n                error: convertError(err),\n              });\n              currentRequestId = null;\n            });\n      }\n      else {\n        // immediate result\n        if (result instanceof Transfer) {\n          worker.send({\n            id: request.id,\n            result: result.message,\n            error: null\n          }, result.transfer);\n        } else {\n          worker.send({\n            id: request.id,\n            result: result,\n            error: null\n          });\n        }\n\n        currentRequestId = null;\n      }\n    }\n    else {\n      throw new Error('Unknown method \"' + request.method + '\"');\n    }\n  }\n  catch (err) {\n    worker.send({\n      id: request.id,\n      result: null,\n      error: convertError(err)\n    });\n  }\n});\n\n/**\n * Register methods to the worker\n * @param {Object} [methods]\n * @param {import('./types.js').WorkerRegisterOptions} [options]\n */\nworker.register = function (methods, options) {\n\n  if (methods) {\n    for (var name in methods) {\n      if (methods.hasOwnProperty(name)) {\n        worker.methods[name] = methods[name];\n        worker.methods[name].worker = publicWorker;\n      }\n    }\n  }\n\n  if (options) {\n    worker.terminationHandler = options.onTerminate;\n    // register listener timeout or default to 1 second\n    worker.abortListenerTimeout = options.abortListenerTimeout || TIMEOUT_DEFAULT;\n  }\n\n  worker.send('ready');\n};\n\nworker.emit = function (payload) {\n  if (currentRequestId) {\n    if (payload instanceof Transfer) {\n      worker.send({\n        id: currentRequestId,\n        isEvent: true,\n        payload: payload.message\n      }, payload.transfer);\n      return;\n    }\n\n    worker.send({\n      id: currentRequestId,\n      isEvent: true,\n      payload\n    });\n  }\n};\n\n\nif (typeof exports !== 'undefined') {\n  exports.add = worker.register;\n  exports.emit = worker.emit;\n}\n", "const {platform, isMainThread, cpus} = require('./environment');\n\n/** @typedef {import(\"./Pool\")} Pool */\n/** @typedef {import(\"./types.js\").WorkerPoolOptions} WorkerPoolOptions */\n/** @typedef {import(\"./types.js\").WorkerRegisterOptions} WorkerRegisterOptions */\n\n/**\n * @template { { [k: string]: (...args: any[]) => any } } T\n * @typedef {import('./types.js').Proxy<T>} Proxy<T>\n */\n\n/**\n * @overload\n * Create a new worker pool\n * @param {WorkerPoolOptions} [script]\n * @returns {Pool} pool\n */\n/**\n * @overload\n * Create a new worker pool\n * @param {string} [script]\n * @param {WorkerPoolOptions} [options]\n * @returns {Pool} pool\n */\nfunction pool(script, options) {\n  var Pool = require('./Pool');\n\n  return new Pool(script, options);\n};\nexports.pool = pool;\n\n/**\n * Create a worker and optionally register a set of methods to the worker.\n * @param {{ [k: string]: (...args: any[]) => any }} [methods]\n * @param {WorkerRegisterOptions} [options]\n */\nfunction worker(methods, options) {\n  var worker = require('./worker');\n  worker.add(methods, options);\n};\nexports.worker = worker;\n\n/**\n * Sends an event to the parent worker pool.\n * @param {any} payload \n */\nfunction workerEmit(payload) {\n  var worker = require('./worker');\n  worker.emit(payload);\n};\nexports.workerEmit = workerEmit;\n\nconst {Promise} = require('./Promise');\nexports.Promise = Promise;\n\nexports.Transfer = require('./transfer');\n\nexports.platform = platform;\nexports.isMainThread = isMainThread;\nexports.cpus = cpus;\n"], "names": ["isNode", "nodeProcess", "versions", "node", "module", "exports", "platform", "process", "worker_threads", "require", "isMainThread", "connected", "Window", "cpus", "self", "navigator", "hardwareConcurrency", "length", "Promise", "handler", "parent", "me", "this", "SyntaxError", "_onSuccess", "_onFail", "resolved", "rejected", "pending", "Symbol", "toStringTag", "_process", "onSuccess", "onFail", "push", "then", "resolve", "reject", "s", "_then", "f", "_resolve", "result", "for<PERSON>ach", "fn", "_reject", "error", "cancel", "CancellationError", "timeout", "delay", "timer", "setTimeout", "TimeoutError", "always", "clearTimeout", "callback", "res", "message", "stack", "Error", "prototype", "finally", "final", "all", "promises", "remaining", "results", "p", "i", "defer", "resolver", "promise", "constructor", "name", "_Promise", "validateOptions", "options", "allowedOptionNames", "objectName", "optionNames", "Object", "keys", "unknownOptionName", "find", "optionName", "includes", "illegalOptionName", "allowedOptionName", "workerOptsNames", "forkOptsNames", "workerThreadOptsNames", "require$$0", "environment", "require$$1", "_require$$2", "require$$2", "TERMINATE_METHOD_ID", "CLEANUP_METHOD_ID", "ensureWorkerThreads", "WorkerThreads", "tryRequireWorkerThreads", "ensureWebWorker", "Worker", "_typeof", "code", "setupBrowserWorker", "script", "workerOpts", "worker", "isBrowserWorker", "on", "event", "addEventListener", "data", "send", "transfer", "postMessage", "setupWorkerThreadWorker", "_options$emitStdStrea", "_options$emitStdStrea2", "workerThreadOpts", "_objectSpread", "stdout", "emitStdStreams", "stderr", "isWorkerThread", "kill", "terminate", "disconnect", "emit", "setupProcessWorker", "child_process", "forkOpts", "fork", "forkArgs", "call", "isChildProcess", "resolveForkOptions", "opts", "processExecArgv", "execArgv", "join", "inspectorActive", "indexOf", "debugBrk", "debugPort", "arg", "assign", "concat", "stdio", "undefined", "objectToError", "obj", "temp", "props", "handleEmittedStdPayload", "payload", "values", "processing", "task", "_task$options", "tracking", "_task$options2", "Worker<PERSON><PERSON>ler", "_options", "onError", "id", "terminated", "create", "Blob", "window", "URL", "createObjectURL", "blob", "embeddedWorker", "type", "__dirname", "getDefaultWorker", "workerType", "setupWorker", "workerTerminateTimeout", "ready", "requestQueue", "toString", "response", "_step", "_iterator", "_createForOfIteratorHelper", "splice", "n", "done", "request", "value", "err", "e", "dispatchQueuedRequests", "isEvent", "terminating", "method", "trackedTask", "timeoutId", "WrappedTimeoutError", "exitCode", "signalCode", "spawnargs", "spawnfile", "cleaning", "<PERSON><PERSON><PERSON><PERSON>", "lastId", "timeoutError", "methods", "exec", "params", "catch", "terminateAndNotify", "busy", "force", "_i", "_Object$values", "cleanup", "removeAllListeners", "killed", "cleanExitTimeout", "once", "WorkerHandlerModule", "_tryRequireWorkerThreads", "_setupProcessWorker", "_setupBrowserWorker", "_setupWorkerThreadWorker", "DEBUG_PORT_ALLOCATOR", "DebugPortAllocator", "ports", "debugPortAllocator", "nextAvailableStartingAt", "starting", "releasePort", "port", "require$$3", "Pool", "workers", "tasks", "freeze", "debugPortStart", "nodeWorker", "maxQueueSize", "Infinity", "onCreateWorker", "onTerminateWorker", "maxWorkers", "isNumber", "isInteger", "TypeError", "validateMaxWorkers", "Math", "max", "minWorkers", "validateMinWorkers", "_ensureMinWorkers", "_boundNext", "_next", "bind", "round", "Array", "isArray", "originalTimeout", "String", "proxy", "arguments", "pool", "slice", "_get<PERSON><PERSON><PERSON>", "shift", "_remove<PERSON><PERSON>ker", "_createWorkerHandler", "_removeWorkerFromList", "index", "removeW<PERSON>ker", "termPromise", "stats", "totalWorkers", "busyWorkers", "filter", "idleWorkers", "pendingTasks", "activeTasks", "overriddenParams", "Pool_1", "Transfer", "exit", "publicWorker", "addAbortListener", "listener", "abortListeners", "parentPort", "convertError", "getOwnPropertyNames", "reduce", "product", "defineProperty", "enumerable", "isPromise", "run", "args", "Function", "apply", "abortListenerTimeout", "terminateAndExit", "_exit", "requestId", "timerId", "map", "timeoutPromise", "settlePromise", "currentRequestId", "register", "hasOwnProperty", "onTerminate", "add", "pool_1", "src", "worker_1", "workerEmit_1", "workerEmit", "require$$4", "platform_1", "isMainThread_1", "cpus_1"], "mappings": ";uRAGA,IAAIA,EAAS,SAAUC,GACrB,YACyB,IAAhBA,GACiB,MAAxBA,EAAYC,UACiB,MAA7BD,EAAYC,SAASC,MACrBF,EAAc,IAAO,oBAGzBG,EAAAC,QAAAL,OAAwBA,EAGxBI,EAAAC,QAAAC,SAA6C,oBAAZC,SAA2BP,EAAOO,SAC/D,OACA,UAIJ,IAAIC,EAA6C,SAA5BJ,EAAOC,QAAQC,UAAuBG,QAAQ,kBACnEL,EAAAC,QAAAK,aAA0D,SAA5BN,EAAOC,QAAQC,WACtCE,GAAkBA,EAAeE,gBAAkBH,QAAQI,UAC5C,oBAAXC,OAGXR,EAAAC,QAAAQ,KAAkD,YAA5BT,EAAOC,QAAQC,SACjCQ,KAAKC,UAAUC,oBACfP,QAAQ,MAAMI,OAAOI,6DCjBzB,SAASC,EAAQC,EAASC,GACxB,IAAIC,EAAKC,KAET,KAAMA,gBAAgBJ,GACpB,MAAM,IAAIK,YAAY,oDAGxB,GAAuB,mBAAZJ,EACT,MAAM,IAAII,YAAY,uDAGxB,IAAIC,EAAa,GACbC,EAAU,GAMdH,KAAKI,UAAW,EAIhBJ,KAAKK,UAAW,EAIhBL,KAAKM,SAAU,EAIfN,KAAKO,OAAOC,aAAe,UAS3B,IAAIC,EAAW,SAAUC,EAAWC,GAClCT,EAAWU,KAAKF,GAChBP,EAAQS,KAAKD,IAWfX,KAAKa,KAAO,SAAUH,EAAWC,GAC/B,OAAO,IAAIf,GAAQ,SAAUkB,EAASC,GACpC,IAAIC,EAAIN,EAAYO,EAAMP,EAAWI,EAASC,GAAUD,EACpDI,EAAIP,EAAYM,EAAMN,EAAWG,EAASC,GAAUA,EAExDN,EAASO,EAAGE,KACXnB,IAQL,IAAIoB,EAAW,SAAUC,GAgBvB,OAdArB,EAAGK,UAAW,EACdL,EAAGM,UAAW,EACdN,EAAGO,SAAU,EAEbJ,EAAWmB,SAAQ,SAAUC,GAC3BA,EAAGF,EACT,IAEIX,EAAW,SAAUC,EAAWC,GAC9BD,EAAUU,IAGZD,EAAWI,EAAU,WAAY,EAE1BxB,GAQLwB,EAAU,SAAUC,GAgBtB,OAdAzB,EAAGK,UAAW,EACdL,EAAGM,UAAW,EACdN,EAAGO,SAAU,EAEbH,EAAQkB,SAAQ,SAAUC,GACxBA,EAAGE,EACT,IAEIf,EAAW,SAAUC,EAAWC,GAC9BA,EAAOa,IAGTL,EAAWI,EAAU,WAAY,EAE1BxB,GAOTC,KAAKyB,OAAS,WAQZ,OAPI3B,EACFA,EAAO2B,SAGPF,EAAQ,IAAIG,GAGP3B,GAUTC,KAAK2B,QAAU,SAAUC,GACvB,GAAI9B,EACFA,EAAO6B,QAAQC,OAEZ,CACH,IAAIC,EAAQC,YAAW,WACrBP,EAAQ,IAAIQ,EAAa,2BAA6BH,EAAQ,UAC7DA,GAEH7B,EAAGiC,QAAO,WACRC,aAAaJ,EACrB,GACA,CAEI,OAAO9B,GAITF,GAAQ,SAAUuB,GAChBD,EAASC,MACR,SAAUI,GACXD,EAAQC,EACZ,GACA,CAUA,SAASP,EAAMiB,EAAUpB,EAASC,GAChC,OAAO,SAAUK,GACf,IACE,IAAIe,EAAMD,EAASd,GACfe,GAA2B,mBAAbA,EAAItB,MAA+C,mBAAjBsB,EAAW,MAE7DA,EAAItB,KAAKC,EAASC,GAGlBD,EAAQqB,GAGZ,MAAOX,GACLT,EAAOS,EACb,EAEA,CA8FA,SAASE,EAAkBU,GACzBpC,KAAKoC,QAAUA,GAAW,oBAC1BpC,KAAKqC,OAAS,IAAIC,OAASD,KAC7B,CAcA,SAASN,EAAaK,GACpBpC,KAAKoC,QAAUA,GAAW,mBAC1BpC,KAAKqC,OAAS,IAAIC,OAASD,KAC7B,YA1GAzC,EAAQ2C,UAAiB,MAAI,SAAU5B,GACrC,OAAOX,KAAKa,KAAK,KAAMF,IAYzBf,EAAQ2C,UAAUP,OAAS,SAAUV,GACnC,OAAOtB,KAAKa,KAAKS,EAAIA,IASvB1B,EAAQ2C,UAAUC,QAAU,SAAUlB,GACpC,IAAMvB,EAAKC,KAELyC,EAAQ,WACZ,OAAO,IAAI7C,GAAQ,SAACkB,GAAO,OAAKA,GAAS,IACtCD,KAAKS,GACLT,MAAK,WAAA,OAAMd,MAGhB,OAAOC,KAAKa,KAAK4B,EAAOA,IAS1B7C,EAAQ8C,IAAM,SAAUC,GACtB,OAAO,IAAI/C,GAAQ,SAAUkB,EAASC,GACpC,IAAI6B,EAAYD,EAAShD,OACrBkD,EAAU,GAEVD,EACFD,EAAStB,SAAQ,SAAUyB,EAAGC,GAC5BD,EAAEjC,MAAK,SAAUO,GACfyB,EAAQE,GAAK3B,EAEI,KADjBwB,GAEE9B,EAAQ+B,MAET,SAAUrB,GACXoB,EAAY,EACZ7B,EAAOS,EACjB,GACA,IAGMV,EAAQ+B,EAEd,KAOAjD,EAAQoD,MAAQ,WACd,IAAIC,EAAW,CAAA,EAOf,OALAA,EAASC,QAAU,IAAItD,GAAQ,SAAUkB,EAASC,GAChDkC,EAASnC,QAAUA,EACnBmC,EAASlC,OAASA,CACtB,IAESkC,GAaTvB,EAAkBa,UAAY,IAAID,MAClCZ,EAAkBa,UAAUY,YAAcb,MAC1CZ,EAAkBa,UAAUa,KAAO,oBAEnCxD,EAAQ8B,kBAAoBA,EAa5BK,EAAaQ,UAAY,IAAID,MAC7BP,EAAaQ,UAAUY,YAAcb,MACrCP,EAAaQ,UAAUa,KAAO,eAE9BxD,EAAQmC,aAAeA,EAGvBsB,EAAAzD,QAAkBA,q5DCjTlB0D,EAAAA,gBAA0B,SAAyBC,EAASC,EAAoBC,GAC9E,GAAKF,EAAL,CAIA,IAAIG,EAAcH,EAAWI,OAAOC,KAAKL,GAAW,GAGhDM,EAAoBH,EAAYI,MAAK,SAAAC,GAAU,OAAKP,EAAmBQ,SAASD,MACpF,GAAIF,EACF,MAAM,IAAIvB,MAAM,WAAamB,EAAa,iCAAmCI,EAAoB,KAInG,IAAII,EAAoBT,EAAmBM,MAAK,SAAAI,GAC9C,OAAOP,OAAOpB,UAAU2B,KAAuBR,EAAYM,SAASE,EACxE,IACE,GAAID,EACF,MAAM,IAAI3B,MAAM,WAAamB,EAAa,mCAAqCQ,EAA/D,0LAKlB,OAAOV,CApBT,GAwBAD,EAAAa,gBAA0B,CACxB,cAAe,OAAQ,QAGzBb,EAAAc,cAAwB,CACtB,MAAO,WAAY,MAAO,WAAY,WAAY,MAAO,gBACzD,SAAU,aAAc,SAAU,QAAS,MAAO,2BAClD,WAIFd,EAAAe,sBAAgC,CAC9B,OAAQ,MAAO,OAAQ,WAAY,QAAS,SAAU,SAAU,aAChE,oBAAqB,eAAgB,iBAAkB,kDC/CzD,IAAKzE,EAAW0E,IAAX1E,QACD2E,EAAcC,EAClBC,EAAiFC,IAA1EpB,EAAemB,EAAfnB,gBAAiBc,EAAaK,EAAbL,cAAeC,EAAqBI,EAArBJ,sBAAuBF,EAAeM,EAAfN,gBAM1DQ,EAAsB,2BAMtBC,EAAoB,yBAExB,SAASC,IACP,IAAIC,EAAgBC,IACpB,IAAKD,EACH,MAAM,IAAIxC,MAAM,+EAGlB,OAAOwC,CACT,CAGA,SAASE,IAEP,GAAsB,mBAAXC,SAA4C,YAAL,oBAANA,OAAM,YAAAC,EAAND,UAA+D,mBAAjCA,OAAO1C,UAAUY,aACzF,MAAM,IAAIb,MAAM,wCAEpB,CAEA,SAASyC,IACP,IACE,OAAO5F,QAAQ,kBACf,MAAMqC,GACN,GAAqB,WAAjB0D,EAAO1D,IAAgC,OAAVA,GAAiC,qBAAfA,EAAM2D,KAEvD,OAAO,KAEP,MAAM3D,CAEZ,CACA,CAgDA,SAAS4D,EAAmBC,EAAQC,EAAYL,GAE9C3B,EAAgBgC,EAAYnB,EAAiB,cAG7C,IAAIoB,EAAS,IAAIN,EAAOI,EAAQC,GAYhC,OAVAC,EAAOC,iBAAkB,EAEzBD,EAAOE,GAAK,SAAUC,EAAOxD,GAC3BlC,KAAK2F,iBAAiBD,GAAO,SAAUtD,GACrCF,EAASE,EAAQwD,KACvB,KAEEL,EAAOM,KAAO,SAAUzD,EAAS0D,GAC/B9F,KAAK+F,YAAY3D,EAAS0D,IAErBP,CACT,CAEA,SAASS,EAAwBX,EAAQP,EAAevB,GAAS,IAAA0C,EAAAC,EAE/D5C,EAAgBC,aAAO,EAAPA,EAAS4C,iBAAkB9B,EAAuB,oBAElE,IAAIkB,EAAS,IAAIT,EAAcG,OAAOI,iWAAMe,CAAA,CAC1CC,OAA+B,QAAzBJ,EAAE1C,aAAO,EAAPA,EAAS+C,sBAAc,IAAAL,GAAAA,EAC/BM,OAA+B,QAAzBL,EAAE3C,aAAO,EAAPA,EAAS+C,sBAAc,IAAAJ,GAAAA,GAC5B3C,aAAO,EAAPA,EAAS4C,mBAqBd,OAnBAZ,EAAOiB,gBAAiB,EACxBjB,EAAOM,KAAO,SAASzD,EAAS0D,GAC9B9F,KAAK+F,YAAY3D,EAAS0D,IAG5BP,EAAOkB,KAAO,WAEZ,OADAzG,KAAK0G,aACE,GAGTnB,EAAOoB,WAAa,WAClB3G,KAAK0G,aAGHnD,SAAAA,EAAS+C,iBACXf,EAAOc,OAAOZ,GAAG,QAAQ,SAACG,GAAI,OAAKL,EAAOqB,KAAK,SAAUhB,MACzDL,EAAOgB,OAAOd,GAAG,QAAQ,SAACG,GAAI,OAAKL,EAAOqB,KAAK,SAAUhB,OAGpDL,CACT,CAEA,SAASsB,EAAmBxB,EAAQ9B,EAASuD,GAE3CxD,EAAgBC,EAAQwD,SAAU3C,EAAe,YAGjD,IAAImB,EAASuB,EAAcE,KACzB3B,EACA9B,EAAQ0D,SACR1D,EAAQwD,UAINlB,EAAON,EAAOM,KAWlB,OAVAN,EAAOM,KAAO,SAAUzD,GACtB,OAAOyD,EAAKqB,KAAK3B,EAAQnD,IAGvBmB,EAAQ+C,iBACVf,EAAOc,OAAOZ,GAAG,QAAQ,SAACG,GAAI,OAAKL,EAAOqB,KAAK,SAAUhB,MACzDL,EAAOgB,OAAOd,GAAG,QAAQ,SAACG,GAAI,OAAKL,EAAOqB,KAAK,SAAUhB,OAG3DL,EAAO4B,gBAAiB,EACjB5B,CACT,CAGA,SAAS6B,EAAmBC,GAC1BA,EAAOA,GAAQ,CAAA,EAEf,IAAIC,EAAkBrI,QAAQsI,SAASC,KAAK,KACxCC,GAA2D,IAAzCH,EAAgBI,QAAQ,aAC1CC,GAAsD,IAA3CL,EAAgBI,QAAQ,eAEnCH,EAAW,GAef,OAdIE,IACFF,EAAS3G,KAAK,aAAeyG,EAAKO,WAE9BD,GACFJ,EAAS3G,KAAK,gBAIlB3B,QAAQsI,SAASlG,SAAQ,SAASwG,GAC5BA,EAAIH,QAAQ,yBAA0B,GACxCH,EAAS3G,KAAKiH,EAEpB,IAESlE,OAAOmE,OAAO,CAAA,EAAIT,EAAM,CAC7BJ,SAAUI,EAAKJ,SACfF,SAAUpD,OAAOmE,OAAO,CAAA,EAAIT,EAAKN,SAAU,CACzCQ,UAAWF,EAAKN,UAAYM,EAAKN,SAASQ,UAAY,IACrDQ,OAAOR,GACRS,MAAOX,EAAKf,eAAiB,YAAQ2B,KAG3C,CAOA,SAASC,EAAeC,GAItB,IAHA,IAAIC,EAAO,IAAI9F,MAAM,IACjB+F,EAAQ1E,OAAOC,KAAKuE,GAEfpF,EAAI,EAAGA,EAAIsF,EAAM1I,OAAQoD,IAChCqF,EAAKC,EAAMtF,IAAMoF,EAAIE,EAAMtF,IAG7B,OAAOqF,CACT,CAEA,SAASE,EAAwBzI,EAAS0I,GAExC5E,OAAO6E,OAAO3I,EAAQ4I,YACnBpH,SAAQ,SAAAqH,GAAI,IAAAC,EAAA,OAAID,SAAa,QAATC,EAAJD,EAAMnF,eAAO,IAAAoF,OAAA,EAAbA,EAAelD,GAAG8C,MAErC5E,OAAO6E,OAAO3I,EAAQ+I,UACnBvH,SAAQ,SAAAqH,GAAI,IAAAG,EAAA,OAAIH,SAAa,QAATG,EAAJH,EAAMnF,eAAO,IAAAsF,OAAA,EAAbA,EAAepD,GAAG8C,KACvC,CAUA,SAASO,EAAczD,EAAQ0D,GAC7B,IAAIhJ,EAAKC,KACLuD,EAAUwF,GAAY,CAAA,EA0F1B,SAASC,EAAQxH,GAGf,IAAK,IAAIyH,KAFTlJ,EAAGmJ,YAAa,EAEDnJ,EAAG0I,gBACUR,IAAtBlI,EAAG0I,WAAWQ,IAChBlJ,EAAG0I,WAAWQ,GAAIhG,SAASlC,OAAOS,GAItCzB,EAAG0I,WAAa9E,OAAOwF,OAAO,KAClC,CAlGEnJ,KAAKqF,OAASA,GAhMhB,WACE,GAA6B,YAAzBd,EAAYvF,SAAwB,CAEtC,GAAoB,oBAAToK,KACT,MAAM,IAAI9G,MAAM,qCAElB,IAAK+G,OAAOC,KAA6C,mBAA/BD,OAAOC,IAAIC,gBACnC,MAAM,IAAIjH,MAAM,oDAIlB,IAAIkH,EAAO,IAAIJ,KAAK,UCvDxBK,EAAiB,24LDuDgD,CAACC,KAAM,oBACpE,OAAOL,OAAOC,IAAIC,gBAAgBC,EACtC,CAGI,OAAOG,UAAY,YAEvB,CA8K0BC,GACxB5J,KAAKuF,OA7KP,SAAqBF,EAAQ9B,GAC3B,GAA2B,QAAvBA,EAAQsG,WAEV,OADA7E,IACOI,EAAmBC,EAAQ9B,EAAQ+B,WAAYL,QACjD,GAA2B,WAAvB1B,EAAQsG,WAEjB,OAAO7D,EAAwBX,EAD/BP,EAAgBD,IACsCtB,GACjD,GAA2B,YAAvBA,EAAQsG,YAA6BtG,EAAQsG,WAEjD,CACL,GAA6B,YAAzBtF,EAAYvF,SAEd,OADAgG,IACOI,EAAmBC,EAAQ9B,EAAQ+B,WAAYL,QAGtD,IAAIH,EAAgBC,IACpB,OAAID,EACKkB,EAAwBX,EAAQP,EAAevB,GAE/CsD,EAAmBxB,EAAQ+B,EAAmB7D,GAAUpE,QAAQ,iBAG/E,CAdI,OAAO0H,EAAmBxB,EAAQ+B,EAAmB7D,GAAUpE,QAAQ,iBAe3E,CAsJgB2K,CAAY9J,KAAKqF,OAAQ9B,GACvCvD,KAAK4H,UAAYrE,EAAQqE,UACzB5H,KAAK+G,SAAWxD,EAAQwD,SACxB/G,KAAKiH,SAAW1D,EAAQ0D,SACxBjH,KAAKsF,WAAa/B,EAAQ+B,WAC1BtF,KAAKmG,iBAAmB5C,EAAQ4C,iBAChCnG,KAAK+J,uBAAyBxG,EAAQwG,uBAGjC1E,IACHrF,KAAKuF,OAAOyE,OAAQ,GAItBhK,KAAKiK,aAAe,GAEpBjK,KAAKuF,OAAOE,GAAG,UAAU,SAAUG,GACjC0C,EAAwBvI,EAAI,CAACsG,OAAUT,EAAKsE,YAChD,IACElK,KAAKuF,OAAOE,GAAG,UAAU,SAAUG,GACjC0C,EAAwBvI,EAAI,CAACwG,OAAUX,EAAKsE,YAChD,IAEElK,KAAKuF,OAAOE,GAAG,WAAW,SAAU0E,GAClC,IAAIpK,EAAGmJ,WAGP,GAAwB,iBAAbiB,GAAsC,UAAbA,EAClCpK,EAAGwF,OAAOyE,OAAQ,EAwEtB,WACA,IACgDI,EADhDC,EAAAC,EACuBvK,EAAGkK,aAAaM,OAAO,IAAE,IAA9C,IAAAF,EAAArJ,MAAAoJ,EAAAC,EAAAG,KAAAC,MAAgD,CAAA,IAAtCC,EAAON,EAAAO,MACf5K,EAAGwF,OAAOM,KAAK6E,EAAQtI,QAASsI,EAAQ5E,SAC9C,CAAK,CAAA,MAAA8E,GAAAP,EAAAQ,EAAAD,EAAA,CAAA,QAAAP,EAAAnJ,GAAA,CACL,CA5EM4J,OACK,CAEL,IA2BMpC,EA3BFO,EAAKkB,EAASlB,GAElB,QAAahB,KADTS,EAAO3I,EAAG0I,WAAWQ,IAEnBkB,EAASY,QACPrC,EAAKnF,SAAsC,mBAApBmF,EAAKnF,QAAQkC,IACtCiD,EAAKnF,QAAQkC,GAAG0E,EAAS5B,iBAIpBxI,EAAG0I,WAAWQ,IAGE,IAAnBlJ,EAAGiL,aAELjL,EAAG2G,YAIDyD,EAAS3I,MACXkH,EAAKzF,SAASlC,OAAOmH,EAAciC,EAAS3I,QAG5CkH,EAAKzF,SAASnC,QAAQqJ,EAAS/I,mBAMtB6G,KADTS,EAAO3I,EAAG6I,SAASK,KAEjBkB,EAASY,SACPrC,EAAKnF,SAAsC,mBAApBmF,EAAKnF,QAAQkC,IACtCiD,EAAKnF,QAAQkC,GAAG0E,EAAS5B,SAMjC,GAAI4B,EAASc,SAAWrG,EAAmB,CACzC,IAAIsG,EAAcnL,EAAG6I,SAASuB,EAASlB,SACnBhB,IAAhBiD,IACEf,EAAS3I,OACXS,aAAaiJ,EAAYC,WACzBD,EAAYjI,SAASlC,OAAOmH,EAAciC,EAAS3I,UAEnDzB,EAAG6I,UAAY3G,aAAaiJ,EAAYC,WAExCD,EAAYjI,SAASlC,OAAO,IAAIqK,EAAoBF,EAAY1J,iBAG7DzB,EAAG6I,SAASK,EAC3B,CACA,CACA,IAuBE,IAAI1D,EAASvF,KAAKuF,OAElBvF,KAAKuF,OAAOE,GAAG,QAASuD,GACxBhJ,KAAKuF,OAAOE,GAAG,QAAQ,SAAU4F,EAAUC,GACzC,IAAIlJ,EAAU,8CAEdA,GAAW,kBAAoBiJ,EAAW,MAC1CjJ,GAAW,oBAAsBkJ,EAAa,MAE9ClJ,GAAW,2BAA8BrC,EAAGsF,OAAS,MACrDjD,GAAW,mBAAsBmD,EAAOgG,UAAY,MACpDnJ,GAAW,mBAAqBmD,EAAOiG,UAAY,MAEnDpJ,GAAW,gBAAkBmD,EAAOc,OAAS,MAC7CjE,GAAW,gBAAkBmD,EAAOgB,OAAS,MAE7CyC,EAAQ,IAAI1G,MAAMF,GACtB,IAEEpC,KAAKyI,WAAa9E,OAAOwF,OAAO,MAChCnJ,KAAK4I,SAAWjF,OAAOwF,OAAO,MAC9BnJ,KAAKgL,aAAc,EACnBhL,KAAKkJ,YAAa,EAClBlJ,KAAKyL,UAAW,EAChBzL,KAAK0L,mBAAqB,KAC1B1L,KAAK2L,OAAS,CAChB,CA6PA,SAASP,EAAoBQ,GAC3B5L,KAAKwB,MAAQoK,EACb5L,KAAKqC,OAAS,IAAIC,OAASD,KAC7B,QA1PAyG,EAAcvG,UAAUsJ,QAAU,WAChC,OAAO7L,KAAK8L,KAAK,YAWnBhD,EAAcvG,UAAUuJ,KAAO,SAASb,EAAQc,EAAQ9I,EAAUM,GAC3DN,IACHA,EAAWrD,EAAQoD,SAIrB,IAAIiG,IAAOjJ,KAAK2L,OAGhB3L,KAAKyI,WAAWQ,GAAM,CACpBA,GAAIA,EACJhG,SAAUA,EACVM,QAASA,GAIX,IAAImH,EAAU,CACZtI,QAAS,CACP6G,GAAIA,EACJgC,OAAQA,EACRc,OAAQA,GAEVjG,SAAUvC,GAAWA,EAAQuC,UAG3B9F,KAAKkJ,WACPjG,EAASlC,OAAO,IAAIuB,MAAM,yBACjBtC,KAAKuF,OAAOyE,MAErBhK,KAAKuF,OAAOM,KAAK6E,EAAQtI,QAASsI,EAAQ5E,UAE1C9F,KAAKiK,aAAarJ,KAAK8J,GAIzB,IAAI3K,EAAKC,KACT,OAAOiD,EAASC,QAAQ8I,OAAM,SAAUxK,GACtC,GAAIA,aAAiB5B,EAAQ8B,mBAAqBF,aAAiB5B,EAAQmC,aAqDzE,OApDAhC,EAAG6I,SAASK,GAAM,CAChBA,GAAAA,EACAhG,SAAUrD,EAAQoD,QAClBO,QAASA,EACT/B,MAAAA,UAKKzB,EAAG0I,WAAWQ,GAErBlJ,EAAG6I,SAASK,GAAIhG,SAASC,QAAUnD,EAAG6I,SAASK,GAAIhG,SAASC,QAAQ8I,OAAM,SAASpB,GAMjF,UALO7K,EAAG6I,SAASK,GAKf2B,aAAeQ,EACjB,MAAMR,EAAIpJ,MAGZ,IAAI0B,EAAUnD,EAAGkM,oBAAmB,GACjCpL,MAAK,WACJ,MAAM+J,KACL,SAASA,GACV,MAAMA,CAClB,IAEQ,OAAO1H,CACf,IAEMnD,EAAGwF,OAAOM,KAAK,CACboD,GAAAA,EACAgC,OAAQrG,IAeV7E,EAAG6I,SAASK,GAAIkC,UAAYrJ,YAAW,WACnC/B,EAAG6I,SAASK,GAAIhG,SAASlC,OAAOS,EAC1C,GAASzB,EAAGgK,wBAEChK,EAAG6I,SAASK,GAAIhG,SAASC,QAEhC,MAAM1B,CAEZ,KAOAsH,EAAcvG,UAAU2J,KAAO,WAC7B,OAAOlM,KAAKyL,UAAY9H,OAAOC,KAAK5D,KAAKyI,YAAY9I,OAAS,GAWhEmJ,EAAcvG,UAAUmE,UAAY,SAAUyF,EAAOjK,GACnD,IAAInC,EAAKC,KACT,GAAImM,EAAO,CAET,IAAK,IAAIlD,KAAMjJ,KAAKyI,gBACUR,IAAxBjI,KAAKyI,WAAWQ,IAClBjJ,KAAKyI,WAAWQ,GAAIhG,SAASlC,OAAO,IAAIuB,MAAM,sBAIlDtC,KAAKyI,WAAa9E,OAAOwF,OAAO,KACpC,CAGE,IAAA,IAAAiD,IAAAC,EAAiB1I,OAAO6E,OAAOzI,EAAG6I,UAASwD,EAAAC,EAAA1M,OAAAyM,IAAE,CAAxC,IAAI1D,EAAI2D,EAAAD,GACXnK,aAAayG,EAAKyC,WAClBzC,EAAKzF,SAASlC,OAAO,IAAIuB,MAAM,sBACnC,CAOE,GALAvC,EAAG6I,SAAWjF,OAAOwF,OAAO,MAEJ,mBAAbjH,IACTlC,KAAK0L,mBAAqBxJ,GAEvBlC,KAAKkM,OAgERlM,KAAKgL,aAAc,MAhEH,CAEhB,IAAIsB,EAAU,SAAS1B,GAUrB,GATA7K,EAAGmJ,YAAa,EAChBnJ,EAAG0L,UAAW,EAEG,MAAb1L,EAAGwF,QAAkBxF,EAAGwF,OAAOgH,oBAEjCxM,EAAGwF,OAAOgH,mBAAmB,WAE/BxM,EAAGwF,OAAS,KACZxF,EAAGiL,aAAc,EACbjL,EAAG2L,mBACL3L,EAAG2L,mBAAmBd,EAAK7K,QACtB,GAAI6K,EACT,MAAMA,GAIV,GAAI5K,KAAKuF,OAAQ,CACf,GAAgC,mBAArBvF,KAAKuF,OAAOkB,KAAqB,CAC1C,GAAIzG,KAAKuF,OAAOiH,OAEd,YADAF,EAAQ,IAAIhK,MAAM,2BAKpB,IAAImK,EAAmB3K,YAAW,WAC5B/B,EAAGwF,QACLxF,EAAGwF,OAAOkB,MAEtB,GAAWzG,KAAK+J,wBAmBR,OAjBA/J,KAAKuF,OAAOmH,KAAK,QAAQ,WACvBzK,aAAawK,GACT1M,EAAGwF,SACLxF,EAAGwF,OAAOiH,QAAS,GAErBF,GACV,IAEYtM,KAAKuF,OAAOyE,MACdhK,KAAKuF,OAAOM,KAAKlB,GAEjB3E,KAAKiK,aAAarJ,KAAK,CAAEwB,QAASuC,SAKpC3E,KAAKyL,UAAW,GAGb,GAAqC,mBAA1BzL,KAAKuF,OAAOmB,UAK1B,MAAM,IAAIpE,MAAM,8BAJhBtC,KAAKuF,OAAOmB,YACZ1G,KAAKuF,OAAOiH,QAAS,CAK7B,CACIF,GACJ,GAiBAxD,EAAcvG,UAAU0J,mBAAqB,SAAUE,EAAOxK,GAC5D,IAAIsB,EAAWrD,EAAQoD,QAWvB,OAVIrB,GACFsB,EAASC,QAAQvB,QAAQA,GAE3B3B,KAAK0G,UAAUyF,GAAO,SAASvB,EAAKrF,GAC9BqF,EACF3H,EAASlC,OAAO6J,GAEhB3H,EAASnC,QAAQyE,EAEvB,IACStC,EAASC,SAalByJ,EAAA5N,QAAiB+J,EACjB6D,EAAA5N,QAAA6N,yBAA0C7H,EAC1C4H,EAAA5N,QAAA8N,oBAAqChG,EACrC8F,EAAA5N,QAAA+N,oBAAqC1H,EACrCuH,EAAA5N,QAAAgO,yBAA0C/G,EAC1C2G,EAAA5N,QAAA8F,oBAAqCA,2CE9nBrC,IAAKjF,EAAW0E,IAAX1E,QACDkJ,EAAgBtE,IAChBD,EAAcG,EAEdsI,EAAuB,6BCA3B,SAASC,IACPjN,KAAKkN,MAAQvJ,OAAOwF,OAAO,MAC3BnJ,KAAKL,OAAS,CAChB,YAJAwN,EAAiBF,EAMjBA,EAAmB1K,UAAU6K,wBAA0B,SAASC,GAC9D,MAAgC,IAAzBrN,KAAKkN,MAAMG,IAChBA,IAGF,GAAIA,GAZU,MAaZ,MAAM,IAAI/K,MAAM,wCAA0C+K,EAA1C,YAKlB,OAFArN,KAAKkN,MAAMG,IAAY,EACvBrN,KAAKL,SACE0N,GAGTJ,EAAmB1K,UAAU+K,YAAc,SAASC,UAC3CvN,KAAKkN,MAAMK,GAClBvN,KAAKL,YDtBkB6N,IASzB,SAASC,EAAKpI,EAAQ9B,GACE,iBAAX8B,EAETrF,KAAKqF,OAASA,GAAU,MAGxBrF,KAAKqF,OAAS,KACd9B,EAAU8B,GAIZrF,KAAK0N,QAAU,GAEf1N,KAAK2N,MAAQ,GAEbpK,EAAUA,GAAW,CAAA,EAGrBvD,KAAKiH,SAAWtD,OAAOiK,OAAOrK,EAAQ0D,UAAY,IAElDjH,KAAK+G,SAAWpD,OAAOiK,OAAOrK,EAAQwD,UAAY,IAElD/G,KAAKsF,WAAa3B,OAAOiK,OAAOrK,EAAQ+B,YAAc,IAEtDtF,KAAKmG,iBAAmBxC,OAAOiK,OAAOrK,EAAQ4C,kBAAoB,IAElEnG,KAAK6N,eAAkBtK,EAAQsK,gBAAkB,MAEjD7N,KAAK8N,WAAavK,EAAQuK,WAI1B9N,KAAK6J,WAAatG,EAAQsG,YAActG,EAAQuK,YAAc,OAE9D9N,KAAK+N,aAAexK,EAAQwK,cAAgBC,IAE5ChO,KAAK+J,uBAAyBxG,EAAQwG,wBAA0B,IAGhE/J,KAAKiO,eAAiB1K,EAAQ0K,gBAAmB,WAAA,OAAM,MAEvDjO,KAAKkO,kBAAoB3K,EAAQ2K,mBAAsB,WAAA,OAAM,MAG7DlO,KAAKsG,eAAiB/C,EAAQ+C,iBAAkB,EAG5C/C,GAAW,eAAgBA,IA6XjC,SAA4B4K,GAC1B,IAAKC,EAASD,KAAgBE,EAAUF,IAAeA,EAAa,EAClE,MAAM,IAAIG,UAAU,mDAExB,CAhYIC,CAAmBhL,EAAQ4K,YAE3BnO,KAAKmO,WAAa5K,EAAQ4K,YAG1BnO,KAAKmO,WAAaK,KAAKC,KAAKlK,EAAYhF,MAAQ,GAAK,EAAG,GAGtDgE,GAAW,eAAgBA,IACH,QAAvBA,EAAQmL,WAET1O,KAAK0O,WAAa1O,KAAKmO,aA4X7B,SAA4BO,GAC1B,IAAKN,EAASM,KAAgBL,EAAUK,IAAeA,EAAa,EAClE,MAAM,IAAIJ,UAAU,mDAExB,CA9XMK,CAAmBpL,EAAQmL,YAC3B1O,KAAK0O,WAAanL,EAAQmL,WAC1B1O,KAAKmO,WAAaK,KAAKC,IAAIzO,KAAK0O,WAAY1O,KAAKmO,aAEnDnO,KAAK4O,qBAIP5O,KAAK6O,WAAa7O,KAAK8O,MAAMC,KAAK/O,MAGV,WAApBA,KAAK6J,YACPf,EAAcjE,qBAElB,CAuXA,SAASuJ,EAASzD,GAChB,MAAwB,iBAAVA,CAChB,CAOA,SAAS0D,EAAU1D,GACjB,OAAO6D,KAAKQ,MAAMrE,IAAUA,CAC9B,QA/VA8C,EAAKlL,UAAUuJ,KAAO,SAAUb,EAAQc,EAAQxI,GAE9C,GAAIwI,IAAWkD,MAAMC,QAAQnD,GAC3B,MAAM,IAAIuC,UAAU,uCAGtB,GAAsB,iBAAXrD,EAAqB,CAC9B,IAAIhI,EAAWrD,EAAQoD,QAEvB,GAAIhD,KAAK2N,MAAMhO,QAAUK,KAAK+N,aAC5B,MAAM,IAAIzL,MAAM,qBAAuBtC,KAAK+N,aAAe,YAI7D,IAAIJ,EAAQ3N,KAAK2N,MACbjF,EAAO,CACTuC,OAASA,EACTc,OAASA,EACT9I,SAAUA,EACVtB,QAAS,KACT4B,QAASA,GAEXoK,EAAM/M,KAAK8H,GAIX,IAAIyG,EAAkBlM,EAASC,QAAQvB,QAgBvC,OAfAsB,EAASC,QAAQvB,QAAU,SAAkBC,GAC3C,OAA4B,IAAxB+L,EAAMjG,QAAQgB,IAEhBA,EAAK/G,QAAUC,EACRqB,EAASC,SAITiM,EAAgBjI,KAAKjE,EAASC,QAAStB,IAKlD5B,KAAK8O,QAEE7L,EAASC,OACpB,CACO,GAAsB,mBAAX+H,EAEd,OAAOjL,KAAK8L,KAAK,MAAO,CAACsD,OAAOnE,GAASc,GAASxI,GAGlD,MAAM,IAAI+K,UAAU,qDAUxBb,EAAKlL,UAAU8M,MAAQ,WACrB,GAAIC,UAAU3P,OAAS,EACrB,MAAM,IAAI2C,MAAM,yBAGlB,IAAIiN,EAAOvP,KACX,OAAOA,KAAK8L,KAAK,WACZjL,MAAK,SAAUgL,GACd,IAAIwD,EAAQ,CAAA,EAQZ,OANAxD,EAAQxK,SAAQ,SAAU4J,GACxBoE,EAAMpE,GAAU,WACd,OAAOsE,EAAKzD,KAAKb,EAAQgE,MAAM1M,UAAUiN,MAAMtI,KAAKoI,YAEhE,IAEeD,CACf,KAuBA5B,EAAKlL,UAAUuM,MAAQ,WACrB,GAAI9O,KAAK2N,MAAMhO,OAAS,EAAG,CAIzB,IAAI4F,EAASvF,KAAKyP,aAClB,GAAIlK,EAAQ,CAEV,IAAIxF,EAAKC,KACL0I,EAAO1I,KAAK2N,MAAM+B,QAGtB,GAAIhH,EAAKzF,SAASC,QAAQ5C,QAAS,CAEjC,IAAI4C,EAAUqC,EAAOuG,KAAKpD,EAAKuC,OAAQvC,EAAKqD,OAAQrD,EAAKzF,SAAUyF,EAAKnF,SACrE1C,KAAKd,EAAG8O,YACR7C,OAAM,WAEL,GAAIzG,EAAO2D,WACT,OAAOnJ,EAAG4P,cAAcpK,EAEtC,IAAa1E,MAAK,WACNd,EAAG+O,OACf,IAGoC,iBAAjBpG,EAAK/G,SACduB,EAAQvB,QAAQ+G,EAAK/G,QAE/B,MAEQ5B,EAAG+O,OAEX,CACA,GAYArB,EAAKlL,UAAUkN,WAAa,WAG1B,IADA,IAAI/B,EAAU1N,KAAK0N,QACV3K,EAAI,EAAGA,EAAI2K,EAAQ/N,OAAQoD,IAAK,CACvC,IAAIwC,EAASmI,EAAQ3K,GACrB,IAAsB,IAAlBwC,EAAO2G,OACT,OAAO3G,CAEb,CAEE,OAAImI,EAAQ/N,OAASK,KAAKmO,YAExB5I,EAASvF,KAAK4P,uBACdlC,EAAQ9M,KAAK2E,GACNA,GAGF,MAWTkI,EAAKlL,UAAUoN,cAAgB,SAASpK,GACtC,IAAIxF,EAAKC,KAQT,OANAgN,EAAqBM,YAAY/H,EAAOqC,WAExC5H,KAAK6P,sBAAsBtK,GAE3BvF,KAAK4O,oBAEE,IAAIhP,GAAQ,SAASkB,EAASC,GACnCwE,EAAOmB,WAAU,GAAO,SAASkE,GAC/B7K,EAAGmO,kBAAkB,CACnBjH,SAAU1B,EAAO0B,SACjBF,SAAUxB,EAAOwB,SACjBZ,iBAAkBZ,EAAOY,iBACzBd,OAAQE,EAAOF,SAEbuF,EACF7J,EAAO6J,GAEP9J,EAAQyE,EAEhB,GACA,KAQAkI,EAAKlL,UAAUsN,sBAAwB,SAAStK,GAE9C,IAAIuK,EAAQ9P,KAAK0N,QAAQhG,QAAQnC,IACnB,IAAVuK,GACF9P,KAAK0N,QAAQnD,OAAOuF,EAAO,IAc/BrC,EAAKlL,UAAUmE,UAAY,SAAUyF,EAAOxK,GAC1C,IAAI5B,EAAKC,KAGTA,KAAK2N,MAAMtM,SAAQ,SAAUqH,GAC3BA,EAAKzF,SAASlC,OAAO,IAAIuB,MAAM,mBACnC,IACEtC,KAAK2N,MAAMhO,OAAS,EAEpB,IAIIoQ,EAJI,SAAUxK,GAChByH,EAAqBM,YAAY/H,EAAOqC,WACxC5H,KAAK6P,sBAAsBtK,IAERwJ,KAAK/O,MAEtB2C,EAAW,GAef,OAdc3C,KAAK0N,QAAQ8B,QACnBnO,SAAQ,SAAUkE,GACxB,IAAIyK,EAAczK,EAAO0G,mBAAmBE,EAAOxK,GAChDd,KAAKkP,GACL/N,QAAO,WACNjC,EAAGmO,kBAAkB,CACnBjH,SAAU1B,EAAO0B,SACjBF,SAAUxB,EAAOwB,SACjBZ,iBAAkBZ,EAAOY,iBACzBd,OAAQE,EAAOF,QAEzB,IACI1C,EAAS/B,KAAKoP,EAClB,IACSpQ,EAAQ8C,IAAIC,IAOrB8K,EAAKlL,UAAU0N,MAAQ,WACrB,IAAIC,EAAelQ,KAAK0N,QAAQ/N,OAC5BwQ,EAAcnQ,KAAK0N,QAAQ0C,QAAO,SAAU7K,GAC9C,OAAOA,EAAO2G,UACbvM,OAEH,MAAO,CACLuQ,aAAeA,EACfC,YAAeA,EACfE,YAAeH,EAAeC,EAE9BG,aAAetQ,KAAK2N,MAAMhO,OAC1B4Q,YAAeJ,IAQnB1C,EAAKlL,UAAUqM,kBAAoB,WACjC,GAAI5O,KAAK0O,WACP,IAAI,IAAI3L,EAAI/C,KAAK0N,QAAQ/N,OAAQoD,EAAI/C,KAAK0O,WAAY3L,IACpD/C,KAAK0N,QAAQ9M,KAAKZ,KAAK4P,yBAU7BnC,EAAKlL,UAAUqN,qBAAuB,WACpC,IAAMY,EAAmBxQ,KAAKiO,eAAe,CAC3ChH,SAAUjH,KAAKiH,SACfF,SAAU/G,KAAK+G,SACfzB,WAAYtF,KAAKsF,WACjBa,iBAAkBnG,KAAKmG,iBACvBd,OAAQrF,KAAKqF,UACT,CAAA,EAEN,OAAO,IAAIyD,EAAc0H,EAAiBnL,QAAUrF,KAAKqF,OAAQ,CAC/D4B,SAAUuJ,EAAiBvJ,UAAYjH,KAAKiH,SAC5CF,SAAUyJ,EAAiBzJ,UAAY/G,KAAK+G,SAC5CzB,WAAYkL,EAAiBlL,YAActF,KAAKsF,WAChDa,iBAAkBqK,EAAiBrK,kBAAoBnG,KAAKmG,iBAC5DyB,UAAWoF,EAAqBI,wBAAwBpN,KAAK6N,gBAC7DhE,WAAY7J,KAAK6J,WACjBE,uBAAwB/J,KAAK+J,uBAC7BzD,eAAgBtG,KAAKsG,kBA4CzBmK,EAAiBhD,uDEhdjB3H,EALA,SAAkB1D,EAAS0D,GACzB9F,KAAKoC,QAAUA,EACfpC,KAAK8F,SAAWA,CAClB,0CCJA,IAAI4K,EAAWpM,IAKX1E,EAAU4E,IAAqB5E,QAW/BgF,EAAoB,yBAQpBW,EAAS,CACXoL,KAAM,WAAW,GAKfC,EAAe,CAMjBC,iBAAkB,SAASC,GACzBvL,EAAOwL,eAAenQ,KAAKkQ,IAO7BlK,KAAMrB,EAAOqB,MAGf,GAAoB,oBAATpH,MAA+C,mBAAhBuG,aAA0D,mBAArBJ,iBAE7EJ,EAAOE,GAAK,SAAUC,EAAOxD,GAC3ByD,iBAAiBD,GAAO,SAAUtD,GAChCF,EAASE,EAAQwD,KACvB,KAEEL,EAAOM,KAAO,SAAUzD,EAAS0D,GAC9BA,EAAWC,YAAY3D,EAAS0D,GAAYC,YAAa3D,QAGzD,IAAuB,oBAAZnD,QAmCd,MAAM,IAAIqD,MAAM,uCAhChB,IAAIwC,EACJ,IACEA,EAAgB3F,QAAQ,kBACxB,MAAMqC,GACN,GAAqB,WAAjB0D,EAAO1D,IAAgC,OAAVA,GAAiC,qBAAfA,EAAM2D,KAGvD,MAAM3D,CAEZ,CAEE,GAAIsD,GAE2B,OAA7BA,EAAckM,WAAqB,CACnC,IAAIA,EAAclM,EAAckM,WAChCzL,EAAOM,KAAOmL,EAAWjL,YAAYgJ,KAAKiC,GAC1CzL,EAAOE,GAAKuL,EAAWvL,GAAGsJ,KAAKiC,GAC/BzL,EAAOoL,KAAO1R,QAAQ0R,KAAK5B,KAAK9P,QACpC,MACIsG,EAAOE,GAAKxG,QAAQwG,GAAGsJ,KAAK9P,SAE5BsG,EAAOM,KAAO,SAAUzD,GACtBnD,QAAQ4G,KAAKzD,IAGfmD,EAAOE,GAAG,cAAc,WACtBxG,QAAQ0R,KAAK,EACnB,IACIpL,EAAOoL,KAAO1R,QAAQ0R,KAAK5B,KAAK9P,QAKpC,CAEA,SAASgS,EAAazP,GACpB,OAAOmC,OAAOuN,oBAAoB1P,GAAO2P,QAAO,SAASC,EAAShO,GAChE,OAAOO,OAAO0N,eAAeD,EAAShO,EAAM,CAC/CuH,MAAOnJ,EAAM4B,GACbkO,YAAY,MAER,GACL,CAQA,SAASC,EAAU5G,GACjB,OAAOA,GAAgC,mBAAfA,EAAM9J,MAAgD,mBAAhB8J,EAAMqB,KACtE,CAGAzG,EAAOsG,QAAU,CAAA,EAQjBtG,EAAOsG,QAAQ2F,IAAM,SAAalQ,EAAImQ,GACpC,IAAIvQ,EAAI,IAAIwQ,SAAS,WAAapQ,EAAK,6BAEvC,OADAJ,EAAEqE,OAASqL,EACJ1P,EAAEyQ,MAAMzQ,EAAGuQ,IAOpBlM,EAAOsG,QAAQA,QAAU,WACvB,OAAOlI,OAAOC,KAAK2B,EAAOsG,UAM5BtG,EAAOmG,wBAAqBzD,EAE5B1C,EAAOqM,qBA3He,IAiItBrM,EAAOwL,eAAiB,GAOxBxL,EAAOsM,iBAAmB,SAAS1M,GACjC,IAAI2M,EAAQ,WACVvM,EAAOoL,KAAKxL,IAGd,IAAII,EAAOmG,mBACT,OAAOoG,IAGT,IAAI1Q,EAASmE,EAAOmG,mBAAmBvG,GACvC,OAAIoM,EAAUnQ,IACZA,EAAOP,KAAKiR,EAAOA,GAEZ1Q,IAEP0Q,IACO,IAAIlS,GAAQ,SAAUuB,EAAUJ,GACrCA,EAAO,IAAIuB,MAAM,sBACvB,MAWAiD,EAAO+G,QAAU,SAASyF,GAExB,IAAKxM,EAAOwL,eAAepR,OASzB,OARA4F,EAAOM,KAAK,CACVoD,GAAI8I,EACJ9G,OAAQrG,EACRpD,MAAOyP,EAAa,IAAI3O,MAAM,yBAKzB,IAAI1C,GAAQ,SAASkB,GAAWA,GAAU,IAInD,IAWIkR,EADErP,EAAW4C,EAAOwL,eAAekB,KAAI,SAAAnB,GAAQ,OAAIA,OAEjDoB,EAAiB,IAAItS,GAAQ,SAACuB,EAAUJ,GAC5CiR,EAAUlQ,YAAW,WACnBf,EAAO,IAAIuB,MAAM,6DACvB,GAAOiD,EAAOqM,qBACd,IAGQO,EAAgBvS,EAAQ8C,IAAIC,GAAU9B,MAAK,WAC/CoB,aAAa+P,GAfRzM,EAAOwL,eAAepR,SACzB4F,EAAOwL,eAAiB,GAgB9B,IAAK,WACD9O,aAAa+P,GAtBbzM,EAAOoL,MAwBX,IASE,OAAO,IAAI/Q,GAAQ,SAASkB,EAASC,GACnCoR,EAActR,KAAKC,EAASC,GAC5BmR,EAAerR,KAAKC,EAASC,EACjC,IAAKF,MAAK,WACN0E,EAAOM,KAAK,CACVoD,GAAI8I,EACJ9G,OAAQrG,EACRpD,MAAO,UAER,SAASoJ,GACVrF,EAAOM,KAAK,CACVoD,GAAI8I,EACJ9G,OAAQrG,EACRpD,MAAOoJ,EAAMqG,EAAarG,GAAO,MAEvC,KAGA,IAAIwH,EAAmB,KAEvB7M,EAAOE,GAAG,WAAW,SAAUiF,GAC7B,GArPwB,6BAqPpBA,EACF,OAAOnF,EAAOsM,iBAAiB,GAGjC,GAAInH,EAAQO,SAAWrG,EACrB,OAAOW,EAAO+G,QAAQ5B,EAAQzB,IAGhC,IACE,IAAIgC,EAAS1F,EAAOsG,QAAQnB,EAAQO,QAEpC,IAAIA,EAsDF,MAAM,IAAI3I,MAAM,mBAAqBoI,EAAQO,OAAS,KArDtDmH,EAAmB1H,EAAQzB,GAG3B,IAAI7H,EAAS6J,EAAO0G,MAAM1G,EAAQP,EAAQqB,QAEtCwF,EAAUnQ,GAEZA,EACKP,MAAK,SAAUO,GACVA,aAAkBsP,EACpBnL,EAAOM,KAAK,CACVoD,GAAIyB,EAAQzB,GACZ7H,OAAQA,EAAOgB,QACfZ,MAAO,MACNJ,EAAO0E,UAEVP,EAAOM,KAAK,CACVoD,GAAIyB,EAAQzB,GACZ7H,OAAQA,EACRI,MAAO,OAGX4Q,EAAmB,IACjC,IACapG,OAAM,SAAUpB,GACfrF,EAAOM,KAAK,CACVoD,GAAIyB,EAAQzB,GACZ7H,OAAQ,KACRI,MAAOyP,EAAarG,KAEtBwH,EAAmB,IACjC,KAIYhR,aAAkBsP,EACpBnL,EAAOM,KAAK,CACVoD,GAAIyB,EAAQzB,GACZ7H,OAAQA,EAAOgB,QACfZ,MAAO,MACNJ,EAAO0E,UAEVP,EAAOM,KAAK,CACVoD,GAAIyB,EAAQzB,GACZ7H,OAAQA,EACRI,MAAO,OAIX4Q,EAAmB,MAOzB,MAAOxH,GACLrF,EAAOM,KAAK,CACVoD,GAAIyB,EAAQzB,GACZ7H,OAAQ,KACRI,MAAOyP,EAAarG,IAE1B,CACA,IAOArF,EAAO8M,SAAW,SAAUxG,EAAStI,GAEnC,GAAIsI,EACF,IAAK,IAAIzI,KAAQyI,EACXA,EAAQyG,eAAelP,KACzBmC,EAAOsG,QAAQzI,GAAQyI,EAAQzI,GAC/BmC,EAAOsG,QAAQzI,GAAMmC,OAASqL,GAKhCrN,IACFgC,EAAOmG,mBAAqBnI,EAAQgP,YAEpChN,EAAOqM,qBAAuBrO,EAAQqO,sBA3UpB,KA8UpBrM,EAAOM,KAAK,UAGdN,EAAOqB,KAAO,SAAU2B,GACtB,GAAI6J,EAAkB,CACpB,GAAI7J,aAAmBmI,EAMrB,YALAnL,EAAOM,KAAK,CACVoD,GAAImJ,EACJrH,SAAS,EACTxC,QAASA,EAAQnG,SAChBmG,EAAQzC,UAIbP,EAAOM,KAAK,CACVoD,GAAImJ,EACJrH,SAAS,EACTxC,QAAAA,GAEN,GAKExJ,EAAAyT,IAAcjN,EAAO8M,SACrBtT,EAAA6H,KAAerB,EAAOqB,YChYxB,IAAO5H,EAAgCsF,EAAhCtF,SAAUI,EAAsBkF,EAAtBlF,aAAcG,EAAQ+E,EAAR/E,KA6B/B,IAAAkT,EAAAC,EAAAnD,KALA,SAAclK,EAAQ9B,GAGpB,OAAO,IAFIiB,IAEJ,CAASa,EAAQ9B,EAC1B,EAYA,IAAAoP,EAAAD,EAAAnN,OAJA,SAAgBsG,EAAStI,GACVmB,IACN8N,IAAI3G,EAAStI,EACtB,EAWA,IAAAqP,EAAAF,EAAAG,WAJA,SAAoBtK,GACL7D,IACNkC,KAAK2B,EACd,EAGO3I,EAAW4N,IAAX5N,QACPyD,EAAAqP,EAAA9S,QAAkBA,EAElB8Q,EAAAgC,EAAAhC,SAAmBoC,IAEnBC,EAAAL,EAAA1T,SAAmBA,EACnBgU,EAAAN,EAAAtT,aAAuBA,EACvB6T,EAAAP,EAAAnT,KAAeA"}