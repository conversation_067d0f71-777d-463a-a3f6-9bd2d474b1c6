{"name": "workerpool", "license": "Apache-2.0", "version": "9.3.3", "description": "Offload tasks to a pool of workers on node.js and in the browser", "homepage": "https://github.com/josdejong/workerpool", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/josdejong)", "repository": {"type": "git", "url": "git://github.com/josdejong/workerpool.git"}, "keywords": ["worker", "web worker", "cluster", "pool", "isomorphic"], "main": "src/index.js", "browser": "dist/workerpool.js", "types": "types/index.d.ts", "files": ["dist", "src", "types", "HISTORY.md", "LICENSE", "README.md"], "scripts": {"build": "rollup -c rollup.config.mjs && npm run build:types", "build:types": "tsc -p .", "watch": "rollup -c rollup.config.mjs -w", "test": "npm run build && mocha test && npm run test:types", "test:types": "tsc -p test/types", "test:debug": "npm run build && mocha debug test", "coverage": "npm run build && c8 mocha && c8 report --reporter=html && echo Coverage report is available at ./coverage/index.html", "prepublishOnly": "npm run test && npm run build"}, "devDependencies": {"@babel/core": "7.27.7", "@babel/preset-env": "7.27.2", "@rollup/plugin-babel": "6.0.4", "@rollup/plugin-commonjs": "28.0.6", "@rollup/plugin-json": "6.1.0", "@rollup/plugin-node-resolve": "16.0.1", "@rollup/plugin-terser": "0.4.4", "@types/node": "24.0.4", "c8": "10.1.3", "core-js": "3.43.0", "date-format": "4.0.14", "find-process": "1.4.10", "fs-extra": "11.3.0", "mocha": "11.7.1", "rollup": "4.44.1", "typescript": "5.8.3"}}