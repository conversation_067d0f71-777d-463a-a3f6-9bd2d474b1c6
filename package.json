{"name": "figma-token-optimizer", "version": "1.0.0", "description": "A Node.js project to optimize Figma token usage by minimizing data size and improving query efficiency", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "test": "node --test test/**/*.test.js", "build": "node scripts/build.js", "optimize": "node scripts/optimize-data.js", "extract-keywords": "node extractKeywords.js", "filter-data": "node filterFigmaData.js", "filter-example": "node filterExample.js", "filter-buttons": "node filterExample.js --button-dimensions"}, "keywords": ["figma", "token", "optimization", "mcp", "design-system", "data-compression"], "author": "Your Name", "license": "MIT", "dependencies": {"js-yaml": "^4.1.0", "lz-string": "^1.5.0", "mocha": "^11.7.1", "node-cache": "^5.1.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}