const filterFigmaData = (data, keywords) => {
  const deepClonedData = JSON.parse(JSON.stringify(data));

  if (!deepClonedData.metadata) {
    return deepClonedData;
  }

  const retainedComponentIds = new Set();
  ['components', 'componentSets'].forEach(key => {
    if (deepClonedData.metadata[key]) {
      const sourceComponents = deepClonedData.metadata[key];
      const filtered = {};
      for (const id in sourceComponents) {
        if (keywords.some(keyword => sourceComponents[id].name.includes(keyword))) {
          filtered[id] = sourceComponents[id];
          retainedComponentIds.add(id);
        }
      }
      deepClonedData.metadata[key] = filtered;
    }
  });

  if (!deepClonedData.nodes || !Array.isArray(deepClonedData.nodes)) {
    return deepClonedData;
  }

  const nodesMap = deepClonedData.nodes.reduce((map, node) => {
    map[node.id] = node;
    return map;
  }, {});

  const retainedNodeIds = new Set();
  const nodeHasRetainedInstance = (nodeId) => {
    const node = nodesMap[nodeId];
    if (!node) return false;

    let hasRetainedChild = false;
    if (node.children) {
      for (const child of node.children) {
        if (nodeHasRetainedInstance(child.id)) {
          hasRetainedChild = true;
        }
      }
    }

    const isRetainedInstance = node.componentId && retainedComponentIds.has(node.componentId);
    if (isRetainedInstance || hasRetainedChild) {
      retainedNodeIds.add(nodeId);
      return true;
    }

    return false;
  };

  const allChildIds = new Set();
  deepClonedData.nodes.forEach(node => {
    if (node.children) {
      node.children.forEach(child => allChildIds.add(child.id));
    }
  });
  const rootNodes = deepClonedData.nodes.filter(node => !allChildIds.has(node.id));

  rootNodes.forEach(rootNode => nodeHasRetainedInstance(rootNode.id));

  const buildFilteredTree = (nodeId) => {
    if (!retainedNodeIds.has(nodeId)) {
      return null;
    }
    const node = { ...nodesMap[nodeId] };
    if (node.children) {
      node.children = node.children
        .map(child => buildFilteredTree(child.id))
        .filter(Boolean);
    }
    return node;
  };

  const filteredTree = rootNodes
    .map(rootNode => buildFilteredTree(rootNode.id))
    .filter(Boolean);

  const finalNodesList = [];
  const collectNodes = (node) => {
      if (!node) return;
      finalNodesList.push(node);
      if (node.children) {
          node.children.forEach(collectNodes);
      }
  };
  filteredTree.forEach(collectNodes);
  
  const finalNodesMap = {};
  finalNodesList.forEach(node => {
      finalNodesMap[node.id] = node;
  });

  deepClonedData.nodes = Object.values(finalNodesMap);

  return deepClonedData;
};

export { filterFigmaData };
