const assert = require('assert');
const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const { filterFigmaData } = require('../src/simple/KeywordFilter');

describe('filterFigmaData', () => {
  it('should filter figma data based on expected keywords', () => {
    const figmaDataPath = path.join(__dirname, '..', 'figma.yml');
    const figmaDataRaw = fs.readFileSync(figmaDataPath, 'utf8');
    const figmaData = yaml.load(figmaDataRaw);

    const expectedKeywords = [
      '尺寸=中按钮, 推荐宽度=中, 颜色=粉(默认), 样式=实色, 状态=正常'
    ];

    const filteredData = filterFigmaData(figmaData, expectedKeywords);

    // Add your assertions here. For example:
    assert.ok(filteredData, 'Filtered data should not be null');
    assert.ok(filteredData.nodes, 'Filtered data should have a "nodes" property');
    
    let found = false;
    function findNode(nodes) {
        if (!nodes) return;
        for (const node of nodes) {
            if (node.name === '尺寸=中按钮, 推荐宽度=中, 颜色=粉(默认), 样式=实色, 状态=正常') {
                found = true;
                break;
            }
            if (node.children) {
                findNode(node.children);
            }
        }
    }

    findNode(filteredData.nodes);

    assert.ok(found, 'The expected component should be in the filtered data');
  });
});