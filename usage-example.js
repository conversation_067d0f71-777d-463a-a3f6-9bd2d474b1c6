import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';
import { filterFigmaData } from './src/simple/KeywordFilter.js';

import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const figmaDataPath = path.join(__dirname, 'figma.yml');
const figmaDataRaw = fs.readFileSync(figmaDataPath, 'utf8');
const figmaData = yaml.load(figmaDataRaw);

const expectedKeywords = [
  '类型=UP主'
];

const filteredData = filterFigmaData(figmaData, expectedKeywords);

console.log('Is data filtered?', JSON.stringify(filteredData) !== JSON.stringify(figmaData));
console.log('--- Filtered Data ---');
console.log(JSON.stringify(figmaData));
